[vite] connecting...
[vite] connected.
Submitting thread: 
Object {title: "test", category: "GENERAL", content: "<p>test</p>"}
Submitting thread: 
Object {title: "test", category: "TECHNICAL_SUPPORT", content: "<p>tttttttttttt</p>"}
API Request failed: 
Object {method: "POST", url: "/api/threads", error: "Please wait 15 seconds before creating another thread"}

Error creating thread: 
Error {}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 15 seconds before creating another thread"}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 15 seconds before creating another thread"}

Submitting thread: 
Object {title: "test", category: "TECHNICAL_SUPPORT", content: "<p>test</p>"}
Submitting thread: 
Object {title: "test", category: "AI_NEWS", content: "<p>test</p>"}
API Request failed: 
Object {method: "POST", url: "/api/threads", error: "Please wait 15 seconds before creating another thread"}

Error creating thread: 
Error {}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 15 seconds before creating another thread"}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 15 seconds before creating another thread"}

Submitting thread: 
Object {title: "test", category: "AI_NEWS", content: "<p>test</p>"}
API Request failed: 
Object {method: "POST", url: "/api/threads", error: "Please wait 3 seconds before creating another thread"}

Error creating thread: 
Error {}
message: "Please wait 3 seconds before creating another thread"
stack: "Error: Please wait 3 seconds before creating another thread↵ at throwIfResNotOk (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/queryClient.ts:17:19)↵ at async apiRequest (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/queryClient.ts:51:5)↵ at async onSubmit (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/pages/new-thread.tsx:196:24)↵ at async https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-FHT2UI52.js?v=acefc7ab:1675:9"
get stack: ƒ ()
set stack: ƒ ()
[[Prototype]]: Object

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 3 seconds before creating another thread"}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 3 seconds before creating another thread"}

Submitting thread: 
Object {title: "test", category: "AI_NEWS", content: "<p>test</p>"}
Submitting thread: 
Object {title: "test", category: "GLOBAL_NEWS", content: "<p>test</p>"}
API Request failed: 
Object {method: "POST", url: "/api/threads", error: "Please wait 23 seconds before creating another thread"}

Error creating thread: 
Error {}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 23 seconds before creating another thread"}

[AI Chat Client Error] Content filter dialog shown
Object {message: "Please wait 23 seconds before creating another thread"}

Submitting thread: 
Object {title: "test", category: "GLOBAL_NEWS", content: "<p>test</p>"}
category: "GLOBAL_NEWS"
content: "<p>test</p>"
title: "test"
[[Prototype]]: Object
Submitting thread: 
Object {title: "test", category: "FUNNY", content: "<p>test</p>"}
category: "FUNNY"
content: "<p>test</p>"
title: "test"
[[Prototype]]: Object
API Request failed: 
Object {method: "POST", url: "/api/threads", error: "Please wait 23 seconds before creating another thread"}
error: "Please wait 23 seconds before creating another thread"
method: "POST"
url: "/api/threads"
[[Prototype]]: Object
