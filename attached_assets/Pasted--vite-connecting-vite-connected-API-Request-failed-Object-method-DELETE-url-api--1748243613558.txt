[vite] connecting...
[vite] connected.
API Request failed: 
Object {method: "DELETE", url: "/api/threads/28", error: "Thread not found or unauthorized"}

at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
API Request failed: 
Object {method: "DELETE", url: "/api/threads/28", error: "Thread not found or unauthorized"}
error: "Thread not found or unauthorized"
method: "DELETE"
url: "/api/threads/28"
[[Prototype]]: Object

Thread deletion error: Thread not found or unauthorized
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
API Request failed: 
Object {method: "DELETE", url: "/api/threads/28", error: "Thread not found or unauthorized"}

at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
API Request failed: 
Object {method: "DELETE", url: "/api/threads/28", error: "Thread not found or unauthorized"}
error: "Thread not found or unauthorized"
method: "DELETE"
url: "/api/threads/28"
[[Prototype]]: Object

Thread deletion error: Thread not found or unauthorized
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400