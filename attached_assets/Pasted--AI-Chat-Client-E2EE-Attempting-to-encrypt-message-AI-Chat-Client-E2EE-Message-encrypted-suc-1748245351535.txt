[AI Chat Client] [E2EE] Attempting to encrypt message
[AI Chat Client] [E2EE] Message encrypted successfully
[AI Chat Client] [E2EE] Message encryption successful
Using cached public key for user 6 (age: 2s)
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipient<PERSON><PERSON><PERSON>ength: 44}
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
Using cached public key for user 6 (age: 5s)
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
[AI Chat Client] [E2EE] Validation passed, attempting to decrypt message
[AI Chat Client] [E2EE] Attempting to decrypt message
[AI Chat Client Error] [E2EE] Decryption failed: Could not decrypt message content
undefined
[E2EE Warning] Decryption returned null with valid inputs - possible key mismatch 
Object {messageLength: 28, nonceLength: 32, senderKeyLength: 44, recipientKeyLength: 44}
messageLength: 28
nonceLength: 32
recipientKeyLength: 44
senderKeyLength: 44
[[Prototype]]: Object
constructor: ƒ Object()
hasOwnProperty: ƒ hasOwnProperty()
isPrototypeOf: ƒ isPrototypeOf()
propertyIsEnumerable: ƒ propertyIsEnumerable()
toLocaleString: ƒ toLocaleString()
toString: ƒ toString()
valueOf: ƒ valueOf()
__defineGetter__: ƒ __defineGetter__()
__defineSetter__: ƒ __defineSetter__()
__lookupGetter__: ƒ __lookupGetter__()
__lookupSetter__: ƒ __lookupSetter__()
__proto__: Object
get __proto__: ƒ get __proto__()
set __proto__: ƒ set __proto__()