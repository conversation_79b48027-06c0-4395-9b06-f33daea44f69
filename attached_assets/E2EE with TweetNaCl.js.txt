Implementation Prompt: E2EE with TweetNaCl.js

Overview
--------
You will add end-to-end encryption (E2EE) to your messaging feature using TweetNaCl.js, a lightweight and audited cryptographic library. The goal is to encrypt messages in the browser before sending them to your Node/Express backend (which only handles encrypted blobs) and decrypt them on the recipient’s side. The server never accesses plaintext.

Prerequisites
-------------
- Frontend: React with TypeScript
- Backend: Node.js with Express (TypeScript)
- Database: PostgreSQL (via Drizzle ORM)
- Dependencies:
  - Install TweetNaCl.js and its utility package:
    npm install tweetnacl tweetnacl-util
- Familiarity with basic public‑key cryptography and API endpoints.

Step 1: Key Pair Generation (Client‑Side)
------------------------------------------
1. On Registration/Login:
   - Generate a key pair using TweetNaCl’s nacl.box.keyPair().
   - Example (TypeScript):
     import nacl from 'tweetnacl';
     import * as naclUtil from 'tweetnacl-util';

     // Generate a key pair
     const keyPair = nacl.box.keyPair();

     // Convert keys to base64 strings for storage/transmission
     const publicKeyBase64 = naclUtil.encodeBase64(keyPair.publicKey);
     const secretKeyBase64 = naclUtil.encodeBase64(keyPair.secretKey);

     // Save secretKeyBase64 securely on the client (e.g., in IndexedDB or session)
     // Send publicKeyBase64 to your server for registration

2. Store the Public Key on the Server:
   - Create an API endpoint (e.g., POST /api/users/:id/public-key) that saves the public key in your PostgreSQL database.

Step 2: Encrypting a Message (Client‑Side)
------------------------------------------
1. Fetch Recipient’s Public Key:
   - Before sending, retrieve the recipient’s public key via an API endpoint (e.g., GET /api/users/:id/public-key).

2. Generate a Nonce:
   - A unique nonce (number used once) is required for each message:
     const nonce = nacl.randomBytes(nacl.box.nonceLength);

3. Encrypt the Message:
   - Convert the plaintext message to a Uint8Array and encrypt using your secret key and the recipient’s public key:
     const messageUint8 = naclUtil.decodeUTF8(plaintextMessage);
     const encryptedMessage = nacl.box(messageUint8, nonce, recipientPublicKey, yourSecretKey);

4. Package for Transmission:
   - Send the following to the server in a POST request (e.g., POST /api/messages):
     - Encrypted message (ciphertext)
     - Nonce (encoded as Base64)
     - Sender and recipient IDs
     - (Optionally) sender’s public key if needed for verification

Step 3: Decrypting a Message (Client‑Side)
------------------------------------------
1. Retrieve the Encrypted Message:
   - Your app receives an encrypted message along with its nonce and sender’s public key.

2. Decrypt the Message:
   - Use your secret key with the sender’s public key and the provided nonce:
     const decryptedMessageUint8 = nacl.box.open(encryptedMessage, nonce, senderPublicKey, yourSecretKey);
     if (!decryptedMessageUint8) {
       throw new Error('Decryption failed');
     }
     const decryptedMessage = naclUtil.encodeUTF8(decryptedMessageUint8);

3. Display the Decrypted Message:
   - Render the plaintext message in your React app.

Step 4: Backend Handling (Express & PostgreSQL)
------------------------------------------------
1. API Endpoints:
   - Store Public Keys:
     - Save the sender’s public key from the client on account registration.
   - Store Messages:
     - Accept POST requests containing the encrypted message, nonce, sender/recipient IDs, and any other necessary metadata.
     - Save these directly to PostgreSQL (using Drizzle ORM) without attempting to decrypt.
   - Retrieve Messages:
     - Provide GET endpoints to retrieve encrypted messages for a recipient. The server simply forwards the data to the client.

2. Security Considerations:
   - Ensure all API communications occur over HTTPS.
   - Validate authentication via Passport.js for each request.

Step 5: Testing and Iteration
-----------------------------
1. Unit Testing:
   - Write tests to verify that a message encrypted on one client can be decrypted correctly on another.
2. Integration Testing:
   - Simulate the entire flow (key generation, public key upload, encryption, message transmission, decryption) to catch any integration issues.
3. Error Handling:
   - Implement error paths for failed decryption and invalid key pairs.
4. Edge Cases:
   - Handle scenarios like missing keys, invalid nonces, or message tampering.

Final Notes
-----------
- Usability: Consider providing a way for users to export/import their private keys for multi‑device access.
- Security: Never send or store private keys on the server.
- Scalability: Once the basic flow is working, consider enhancements like ephemeral key pairs per session for forward secrecy or key rotation mechanisms.

By following this prompt, you'll implement a secure, E2EE messaging system that is safe yet avoids the complexity of protocols like Signal. This approach leverages TweetNaCl.js’s simplicity and audited security, ensuring that your RetroForum messaging remains private while being straightforward to implement.

Feel free to adapt each step as needed for your specific application structure.
