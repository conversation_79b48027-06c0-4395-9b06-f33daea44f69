[vite] connecting...
[vite] connected.
Query failed: 
Error {}
message: "Failed to get encryption status"
stack: "Error: Failed to get encryption status↵ at throwIfResNotOk (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/queryClient.ts:17:19)↵ at async https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/queryClient.ts:89:5"
get stack: ƒ ()
length: 0
name: ""
[[Prototype]]: ƒ ()
apply: ƒ apply()
length: 2
name: "apply"
[[Prototype]]: ƒ ()
arguments: "'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"
get arguments: ƒ ()
set arguments: ƒ ()
bind: ƒ bind()
call: ƒ call()
caller: "'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"
get caller: ƒ ()
set caller: ƒ ()
constructor: ƒ Function()
length: 0
name: ""
toString: ƒ toString()
Symbol(Symbol.hasInstance): undefined
[[Prototype]]: Object
set stack: ƒ ()
length: 1
name: ""
[[Prototype]]: ƒ ()
[[Prototype]]: Object
constructor: ƒ Error()
message: "Failed to get encryption status"
name: "Error"
toString: ƒ toString()
[[Prototype]]: Object

at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
Query failed: 
Error {}
message: "Failed to get encryption status"
stack: "Error: Failed to get encryption status↵ at throwIfResNotOk (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/queryClient.ts:17:19)↵ at async https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/queryClient.ts:89:5"
get stack: ƒ ()
length: 0
name: ""
[[Prototype]]: ƒ ()
set stack: ƒ ()
length: 1
name: ""
[[Prototype]]: ƒ ()
[[Prototype]]: Object
constructor: ƒ Error()
message: "Failed to get encryption status"
name: "Error"
toString: ƒ toString()
length: 0
name: "toString"
[[Prototype]]: ƒ ()
[[Prototype]]: Object
constructor: ƒ Error()
hasOwnProperty: ƒ hasOwnProperty()
isPrototypeOf: ƒ isPrototypeOf()
propertyIsEnumerable: ƒ propertyIsEnumerable()
toLocaleString: ƒ toLocaleString()
toString: ƒ toString()
valueOf: ƒ valueOf()
__defineGetter__: ƒ __defineGetter__()
__defineSetter__: ƒ __defineSetter__()
__lookupGetter__: ƒ __lookupGetter__()
__lookupSetter__: ƒ __lookupSetter__()
__proto__: Object
get __proto__: ƒ get __proto__()
set __proto__: ƒ set __proto__()

at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400