Warning: Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?%s%s 

Check the render method of `SlotClone`. 
    at Badge (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/components/ui/badge.tsx:35:18)
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-GWNN5Q73.js?v=acefc7ab:52:11
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-GWNN5Q73.js?v=acefc7ab:33:11
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-JVRV33CZ.js?v=acefc7ab:41:13
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-GWNN5Q73.js?v=acefc7ab:52:11
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-GWNN5Q73.js?v=acefc7ab:33:11
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-JVRV33CZ.js?v=acefc7ab:41:13
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-IHTEXX6O.js?v=acefc7ab:1980:13
    at https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=acefc7ab:187:13
    at Provider (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-ZE3V6U6Z.js?v=acefc7ab:38:15)
    at Provider (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-IHTEXX6O.js?v=acefc7ab:1915:15)
    at Popper (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-IHTEXX6O.js?v=acefc7ab:1972:11)
    at Tooltip (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=acefc7ab:105:5)
    at Provider (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-ZE3V6U6Z.js?v=acefc7ab:38:15)
    at TooltipProvider (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=acefc7ab:61:5)
    at div
    at div
    at div
    at _c3 (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/components/ui/card.tsx:43:12)
    at div
    at _c (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/components/ui/card.tsx:20:11)
    at div
    at div
    at div
    at main
    at div
    at TerminalLayout (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/components/layout/terminal-layout.tsx:24:34)
    at MessagesPage (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/pages/messages-page.tsx:44:20)
    at Route (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=acefc7ab:323:16)
    at ProtectedRoute (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/lib/protected-route.tsx:22:3)
    at Switch (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=acefc7ab:379:17)
    at Router
    at div
    at AuthProvider (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/hooks/use-auth.tsx:26:32)
    at QueryClientProvider (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=acefc7ab:2805:3)
    at App (https://b7e10a60-1a95-4841-8f4e-210fe9aed7ac-00-25fk1u5yk7ndi.kirk.replit.dev/src/App.tsx?v=QC6KRQG9DVultYTP6QonP:118:35)