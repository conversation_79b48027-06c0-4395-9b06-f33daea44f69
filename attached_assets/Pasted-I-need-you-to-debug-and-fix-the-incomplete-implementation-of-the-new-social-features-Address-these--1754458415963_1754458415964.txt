I need you to debug and fix the incomplete implementation of the new social features. Address these specific issues systematically:

### 1. Friend Request System Failure
- **Diagnosis Checklist:**
  - Verify database schema for `friend_requests` table exists with columns: `id`, `sender_id`, `receiver_id`, `status` (enum: pending/accepted/declined), `created_at`
  - Confirm backend routes exist for:
    - `POST /friend-request/send` (with auth)
    - `POST /friend-request/respond/:request_id` (accept/decline)
    - `GET /friend-requests/pending` (current user)
  - Check frontend API calls:
    - Are request payloads including proper `receiver_id`?
    - Is response handling updating UI state immediately?
  - Test with two authenticated user sessions

### 2. Image Upload Malfunction
- **Critical Fixes:**
  - File handling:
    - Verify multipart/form-data processing middleware (e.g., Multer)
    - Validate file MIME types: `image/jpeg`, `image/png`, `image/webp`
  - Image processing:
    - Implement automatic resizing to 128x128px (use Sharp/Libvips)
    - Convert all formats to WebP for consistency
  - Storage:
    - Store resized images in persistent directory (not tmp)
    - Update `users` table with new avatar path
  - Security:
    - Add file size limit (max 2MB)
    - Sanitize filenames

### 3. Public Profile Viewer Issues
- **Implementation Gap Analysis:**
  - Route: `GET /user/:username` must return:
    ```json
    {
      "username": "string",
      "avatar_url": "string",
      "bio": "string",
      "friend_status": "none/pending/accepted" 
    }
    ```
  - Database: Confirm `users` table has `bio` TEXT column
  - Frontend:
    - Clickable usernames should fetch this endpoint
    - Handle 404 for invalid usernames
    - Display friend action buttons based on `friend_status`

### 4. Cross-Feature Testing Flow
After fixes, validate this user journey:
1. User A uploads avatar → verify `/static/avatars/userA.webp` exists
2. User B views User A's profile → sees bio and new avatar
3. User B sends friend request → User A sees notification
4. User A accepts → Both see mutual friend status
5. User B checks `/friends` page → sees User A in friends list

### Required Output:
- Detailed error logs for each failing component
- Fixed code snippets with brief explanations
- Verification steps for me to test each fix