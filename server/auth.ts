import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { User as SelectUser } from "@shared/schema";
import cors from "cors";

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

const scryptAsync = promisify(scrypt);

export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

export function setupAuth(app: Express) {
  if (!process.env.SESSION_SECRET) {
    throw new Error("SESSION_SECRET must be set");
  }

  const isDev = process.env.NODE_ENV !== "production";

  // Configure passport
  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        console.log(`[Auth] Attempting authentication for user: ${username}`);
        const user = await storage.getUserByUsername(username);

        if (!user || !(await comparePasswords(password, user.password))) {
          console.log(`[Auth] Authentication failed for user: ${username}`);
          return done(null, false, { message: "Invalid username or password" });
        }

        // Check ban status
        if (user.bannedAt) {
          console.log(`[Auth] Banned user attempted login: ${username}`);
          return done(null, false, { 
            message: `Account banned: ${user.banReason || "No reason provided"}`
          });
        }

        // Check suspension status
        if (user.suspendedUntil) {
          const suspendedUntil = new Date(user.suspendedUntil);
          const now = new Date();
          if (suspendedUntil > now) {
            console.log(`[Auth] Suspended user attempted login: ${username}, suspended until: ${suspendedUntil.toISOString()}`);
            return done(null, false, {
              message: `Account suspended until ${suspendedUntil.toISOString()}: ${user.banReason || "No reason provided"}`
            });
          }
        }

        console.log(`[Auth] Authentication successful for user: ${username}`);
        return done(null, user);
      } catch (error) {
        console.error('[Auth] Authentication error:', error);
        return done(error);
      }
    })
  );

  passport.serializeUser((user, done) => {
    console.log(`[Auth] Serializing user session: ${user.id}`);
    done(null, user.id);
  });

  // Simple in-memory cache for user sessions
  const userCache = new Map<number, { user: SelectUser; timestamp: number }>();
  const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  passport.deserializeUser(async (id: number, done) => {
    try {
      // Check cache first
      const cached = userCache.get(id);
      const now = Date.now();
      
      if (cached && (now - cached.timestamp < CACHE_TTL)) {
        // Use cached user data
        return done(null, cached.user);
      }

      console.log(`[Auth] Deserializing user session: ${id}`);
      const user = await storage.getUser(id);
      
      if (!user) {
        console.warn(`[Auth] User with ID ${id} not found during deserialization`);
        userCache.delete(id); // Remove from cache if not found
        return done(null, false);
      }
      
      // Cache the user data
      userCache.set(id, { user, timestamp: now });
      
      done(null, user);
    } catch (error) {
      console.error('[Auth] Deserialization error:', error);
      done(error);
    }
  });

  // Session and auth middleware setup
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    store: storage.sessionStore,
    cookie: {
      secure: !isDev,
      httpOnly: true,
      sameSite: isDev ? 'lax' : 'strict',
      maxAge: 2 * 60 * 60 * 1000 // 2 hours
    }
  };

  if (!isDev) {
    app.set("trust proxy", 1);
  }

  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  // Auth routes
  app.post("/api/login", (req, res, next) => {
    console.log('[Auth] Processing login request');
    passport.authenticate("local", (err: any, user: any, info: any) => {
      if (err) {
        console.error('[Auth] Login error:', err);
        return next(err);
      }

      if (!user) {
        console.log('[Auth] Login failed:', info);
        // Special handling for ban/suspension messages
        if (info?.message?.startsWith("Account banned") || info?.message?.startsWith("Account suspended")) {
          console.log('[Auth] Sending restriction info:', info);
          return res.status(403).json(info);
        }
        return res.status(401).json({ message: info?.message || "Authentication failed" });
      }

      req.login(user, (err) => {
        if (err) {
          console.error('[Auth] Login session error:', err);
          return next(err);
        }
        console.log(`[Auth] User logged in successfully: ${user.id}`);
        res.json(user);
      });
    })(req, res, next);
  });

  app.post("/api/register", async (req, res, next) => {
    try {
      console.log('[Auth] Processing registration request');
      const existingUser = await storage.getUserByUsername(req.body.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      const user = await storage.createUser({
        ...req.body,
        password: await hashPassword(req.body.password),
      });

      req.login(user, (err) => {
        if (err) return next(err);
        res.status(201).json(user);
      });
    } catch (error) {
      next(error);
    }
  });

  app.post("/api/logout", (req, res, next) => {
    req.logout((err) => {
      if (err) return next(err);
      res.sendStatus(200);
    });
  });

  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    res.json(req.user);
  });
}