# Test Environment Configuration

# Base URL of the application to test
BASE_URL=http://localhost:3000

# Headless browser mode (true/false)
HEADLESS=true

# Slow down browser operations by this many milliseconds (helps with stability)
SLOW_MO=50

# Screenshot settings
SCREENSHOTS=true
SCREENSHOT_DIR=./test-screenshots

# Test output directory
OUTPUT_DIR=./test-results

# Timeouts (milliseconds)
NAVIGATION_TIMEOUT=30000
ELEMENT_TIMEOUT=10000
API_TIMEOUT=15000
AI_TIMEOUT=45000

# Test user credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=adminpassword
TEST_USERNAME=testuser
TEST_PASSWORD=testpassword
RECIPIENT_USERNAME=recipient
RECIPIENT_PASSWORD=recipientpassword

# Test retry settings
RETRIES=2

# Viewport size
VIEWPORT_WIDTH=1280
VIEWPORT_HEIGHT=800