// Run all specialized test suites 
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const { runAIChatTests } = require('./specialized/aiChatTests');
const { runEncryptedMessagingTests } = require('./specialized/encryptedMessagingTests');

// Directory for test results
const RESULTS_DIR = './test-results/specialized';

// Ensure results directory exists
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

// Helper to save results to file
function saveResults(name, results) {
  const filename = path.join(RESULTS_DIR, `${name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`);
  fs.writeFileSync(filename, JSON.stringify(results, null, 2));
  console.log(chalk.gray(`Results saved to: ${filename}`));
}

// Main runner function
async function runAllSpecializedTests() {
  console.log(chalk.blue('=== Running All Specialized Test Suites ==='));
  console.log(chalk.gray(`Time: ${new Date().toISOString()}`));
  
  const startTime = Date.now();
  const results = {};
  
  // Run AI chat tests
  console.log(chalk.cyan('\n=== AI Chat Tests ==='));
  try {
    results.aiChat = await runAIChatTests();
    saveResults('ai-chat', results.aiChat);
  } catch (error) {
    console.error(chalk.red('AI Chat test suite failed:'), error);
    results.aiChat = { error: error.message };
  }
  
  // Run encrypted messaging tests
  console.log(chalk.cyan('\n=== Encrypted Messaging Tests ==='));
  try {
    results.encryptedMessaging = await runEncryptedMessagingTests();
    saveResults('encrypted-messaging', results.encryptedMessaging);
  } catch (error) {
    console.error(chalk.red('Encrypted Messaging test suite failed:'), error);
    results.encryptedMessaging = { error: error.message };
  }
  
  // Run additional specialized test suites here
  // ...
  
  // Generate summary
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  // Calculate totals
  let totalTests = 0;
  let totalPassed = 0;
  let totalFailed = 0;
  
  for (const suite in results) {
    if (results[suite].summary) {
      totalTests += results[suite].summary.total || 0;
      totalPassed += results[suite].summary.passed || 0;
      totalFailed += results[suite].summary.failed || 0;
    }
  }
  
  // Display summary
  console.log(chalk.blue('\n=== Specialized Test Suites Summary ==='));
  console.log(chalk.gray(`Total tests: ${totalTests}`));
  console.log(chalk.green(`Total passed: ${totalPassed}`));
  console.log(chalk.red(`Total failed: ${totalFailed}`));
  console.log(chalk.gray(`Overall pass rate: ${totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0}%`));
  console.log(chalk.gray(`Total duration: ${duration}ms (${(duration / 1000 / 60).toFixed(2)} minutes)`));
  
  // Save overall summary
  const summary = {
    timestamp: new Date().toISOString(),
    duration,
    suites: Object.keys(results).length,
    tests: {
      total: totalTests,
      passed: totalPassed,
      failed: totalFailed,
      passRate: totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0
    },
    suiteResults: Object.keys(results).map(name => ({
      name,
      ...results[name].summary
    }))
  };
  
  saveResults('all-specialized-summary', summary);
  
  return summary;
}

// Run everything if this script is executed directly
if (require.main === module) {
  runAllSpecializedTests().then(() => {
    console.log(chalk.blue('All specialized test suites completed'));
  }).catch(error => {
    console.error(chalk.red('Error running test suites:'), error);
    process.exit(1);
  });
}

module.exports = { runAllSpecializedTests };