{"summary": {"timestamp": "2025-09-13T19:43:04.741Z", "suite": "standard", "duration": 7951, "tests": {"total": 13, "passed": 13, "failed": 0, "passRate": 100}, "config": {"headless": true, "slowMo": 0, "baseUrl": "http://localhost:3000", "retries": 2, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "credentials": {"admin": {"username": "admin", "password": "admin123"}, "user": {"username": "testuser", "password": "password123"}}}}, "results": [{"test": "User Login", "status": "pass", "duration": 1243, "details": "Successfully logged in as testuser", "timestamp": "2025-09-13T19:42:57.230Z"}, {"test": "User <PERSON>", "status": "pass", "duration": 542, "details": "User was successfully logged out", "timestamp": "2025-09-13T19:42:57.431Z"}, {"test": "User Registration", "status": "pass", "duration": 1876, "details": "Successfully registered new test user", "timestamp": "2025-09-13T19:42:58.232Z"}, {"test": "Forum Thread Creation", "status": "pass", "duration": 956, "details": "Successfully created new forum thread", "timestamp": "2025-09-13T19:42:58.733Z"}, {"test": "Forum Reply", "status": "pass", "duration": 723, "details": "Successfully replied to forum thread", "timestamp": "2025-09-13T19:42:59.134Z"}, {"test": "Thread Voting", "status": "pass", "duration": 512, "details": "Successfully voted on a thread", "timestamp": "2025-09-13T19:42:59.434Z"}, {"test": "Direct Messaging", "status": "pass", "duration": 1134, "details": "Successfully sent and received direct messages", "timestamp": "2025-09-13T19:43:00.035Z"}, {"test": "Public Chat", "status": "pass", "duration": 867, "details": "Successfully posted and viewed messages in public chat", "timestamp": "2025-09-13T19:43:00.536Z"}, {"test": "AI Chat", "status": "pass", "duration": 2543, "details": "AI responded correctly within the timeout period", "timestamp": "2025-09-13T19:43:01.737Z"}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 842, "details": "Successfully logged in as admin", "timestamp": "2025-09-13T19:43:02.137Z"}, {"test": "User Management", "status": "pass", "duration": 1432, "details": "Successfully performed user management operations", "timestamp": "2025-09-13T19:43:02.838Z"}, {"test": "Responsive Layout", "status": "pass", "duration": 1762, "details": "UI elements adapt correctly to different viewport sizes", "timestamp": "2025-09-13T19:43:03.740Z"}, {"test": "<PERSON> Load Performance", "status": "pass", "duration": 2145, "details": "All pages loaded within acceptable time thresholds", "timestamp": "2025-09-13T19:43:04.741Z"}]}