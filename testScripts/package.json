{"name": "crow-ai-test-suite", "version": "1.0.0", "description": "Comprehensive test suite for Crow-AI application", "main": "run.js", "scripts": {"test": "node run.js standard", "test:all": "node run.js all", "test:visible": "node run.js standard --headless=false", "test:all-visible": "node run.js all --headless=false", "test:auth": "node run.js standard --tests=login,logout,registerUser", "test:forum": "node run.js standard --tests=forumThread,forumReply,threadVoting", "test:messaging": "node run.js standard --tests=directMessaging,publicChat", "test:ai": "node run.js standard --tests=aiChat", "test:admin": "node run.js standard --tests=adminLogin,userManagement", "test:api": "node run.js standard --tests=threadsAPI", "test:ui": "node run.js standard --tests=responsiveLayout,pageLoadPerformance", "test:specialized": "node run.js specialized", "test:specialized-visible": "node run.js specialized --headless=false", "test:ai-deep": "node run.js ai", "test:encryption": "node run.js encryption", "test:ci": "node run.js all --ci --reporter=json", "report": "open ./test-results/latest.html || xdg-open ./test-results/latest.html || start ./test-results/latest.html"}, "keywords": ["testing", "automation", "puppeteer", "e2e", "forum", "crow-ai", "ai-chat"], "author": "", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "dotenv": "^16.0.3", "fs-extra": "^11.1.1", "puppeteer": "^19.7.2", "yargs": "^17.7.1"}, "devDependencies": {"prettier": "^2.8.4"}, "engines": {"node": ">=14.0.0"}}