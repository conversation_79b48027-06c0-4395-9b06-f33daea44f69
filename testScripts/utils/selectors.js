/**
 * Centralized selectors for UI testing
 * 
 * This file contains all the CSS selectors used in the test suite
 * to make it easier to update them if the UI changes.
 */

module.exports = {
  // Auth selectors
  auth: {
    loginForm: '#loginForm',
    usernameInput: 'input[name="username"]',
    passwordInput: 'input[name="password"]',
    loginButton: 'button[type="submit"]',
    registerLink: 'a[href="/register"]',
    registerForm: '#registerForm', 
    registerButton: 'button[type="submit"]',
    logoutButton: '#logoutButton',
    userMenu: '#userMenu',
    errorMessage: '.error-message',
    successMessage: '.success-message'
  },
  
  // Forum selectors
  forum: {
    threadList: '.thread-list',
    threadItem: '.thread-item',
    threadTitle: '.thread-title',
    threadContent: '.thread-content',
    threadAuthor: '.thread-author',
    createThreadButton: '#createThreadButton',
    threadForm: '#threadForm',
    threadTitleInput: 'input[name="title"]',
    threadContentInput: '.tiptap-editor', // TipTap editor container
    threadCategorySelect: 'select[name="category"]',
    submitThreadButton: 'button[type="submit"]',
    threadVoteUp: '.vote-up',
    threadVoteDown: '.vote-down',
    threadVoteCount: '.vote-count',
    replyList: '.reply-list',
    replyItem: '.reply-item',
    replyContent: '.reply-content',
    replyAuthor: '.reply-author',
    replyForm: '#replyForm',
    replyContentInput: '.tiptap-editor', // TipTap editor container
    submitReplyButton: 'button[type="submit"]',
    deleteThreadButton: '.delete-thread',
    deleteReplyButton: '.delete-reply',
    confirmDeleteButton: '.confirm-delete'
  },
  
  // Direct messaging selectors
  messages: {
    contactList: '.contact-list',
    contactItem: '.contact-item',
    messageContainer: '.message-container',
    messageList: '.message-list',
    messageItem: '.message-item',
    messageContent: '.message-content',
    messageSender: '.message-sender',
    messageForm: '#messageForm',
    messageInput: 'textarea[name="message"]',
    recipientSelect: 'select[name="recipient"]',
    sendMessageButton: 'button[type="submit"]',
    encryptToggle: '#encryptToggle',
    encryptedIndicator: '.encrypted-indicator',
    unreadIndicator: '.unread-indicator'
  },
  
  // Chat room selectors
  chat: {
    roomList: '.room-list',
    roomItem: '.room-item',
    messageContainer: '.chat-container',
    messageList: '.chat-message-list',
    messageItem: '.chat-message',
    messageContent: '.chat-message-content',
    messageSender: '.chat-message-sender',
    chatForm: '#chatForm',
    chatInput: 'textarea[name="message"]',
    sendChatButton: 'button[type="submit"]',
    userList: '.user-list',
    userItem: '.user-item'
  },
  
  // AI chat selectors
  aiChat: {
    sessionList: '.ai-session-list',
    sessionItem: '.ai-session-item',
    createSessionButton: '#createAISessionButton',
    deleteSessionButton: '.delete-ai-session',
    renameSessionButton: '.rename-ai-session',
    messageContainer: '.ai-chat-container',
    messageList: '.ai-message-list',
    userMessage: '.ai-user-message',
    assistantMessage: '.ai-assistant-message',
    messageContent: '.ai-message-content',
    chatForm: '#aiChatForm',
    chatInput: 'textarea[name="message"]',
    sendAIChatButton: 'button[type="submit"]',
    attachmentButton: '#attachmentButton',
    attachmentInput: 'input[type="file"]',
    streamingIndicator: '.streaming-indicator',
    errorMessage: '.ai-error-message'
  },
  
  // Profile selectors
  profile: {
    profileContainer: '.profile-container',
    usernameDisplay: '.profile-username',
    avatarDisplay: '.profile-avatar',
    avatarEditor: '.avatar-editor',
    saveAvatarButton: '#saveAvatarButton',
    passwordChangeForm: '#passwordChangeForm',
    currentPasswordInput: 'input[name="currentPassword"]',
    newPasswordInput: 'input[name="newPassword"]',
    confirmPasswordInput: 'input[name="confirmPassword"]',
    changePasswordButton: 'button[name="changePassword"]',
    deleteAccountForm: '#deleteAccountForm',
    confirmDeleteInput: 'input[name="confirmDelete"]',
    deleteAccountButton: 'button[name="deleteAccount"]',
    successMessage: '.success-message',
    errorMessage: '.error-message'
  },
  
  // Admin selectors
  admin: {
    adminPanel: '.admin-panel',
    userList: '.admin-user-list',
    userItem: '.admin-user-item',
    userDetails: '.admin-user-details',
    suspendUserButton: '.suspend-user',
    banUserButton: '.ban-user',
    unsuspendUserButton: '.unsuspend-user',
    unbanUserButton: '.unban-user',
    deleteUserButton: '.delete-user',
    toggleAdminButton: '.toggle-admin',
    confirmActionButton: '.confirm-action',
    cancelActionButton: '.cancel-action',
    userTimeline: '.user-timeline',
    timelineEvent: '.timeline-event',
    adminLogs: '.admin-logs',
    logItem: '.log-item',
    testRunnerSection: '.test-runner-section',
    runTestButton: '#runTestButton',
    testTypeSelect: 'select[name="testType"]',
    testSelect: 'select[name="tests"]',
    testResults: '.test-results',
    testResultItem: '.test-result-item',
    testPass: '.test-pass',
    testFail: '.test-fail'
  },
  
  // Navigation selectors
  navigation: {
    sidebar: '.sidebar',
    navItem: '.nav-item',
    homeLink: 'a[href="/"]',
    forumLink: 'a[href="/forum"]',
    messagesLink: 'a[href="/messages"]',
    chatLink: 'a[href="/chat"]',
    aiChatLink: 'a[href="/ai"]',
    profileLink: 'a[href="/profile"]',
    adminLink: 'a[href="/admin"]',
    toggleSidebarButton: '.toggle-sidebar',
    mobileSidebarToggle: '.mobile-sidebar-toggle'
  },
  
  // UI component selectors
  ui: {
    toast: '.toast',
    toastMessage: '.toast-message',
    toastClose: '.toast-close',
    modal: '.modal',
    modalContent: '.modal-content',
    modalClose: '.modal-close',
    dialog: '.dialog',
    dialogContent: '.dialog-content',
    dialogConfirm: '.dialog-confirm',
    dialogCancel: '.dialog-cancel',
    loader: '.loader',
    skeleton: '.skeleton',
    errorBoundary: '.error-boundary',
    errorMessage: '.error-message',
    retryButton: '.retry-button',
    pagination: '.pagination',
    paginationItem: '.pagination-item',
    paginationPrev: '.pagination-prev',
    paginationNext: '.pagination-next',
    dropdown: '.dropdown',
    dropdownTrigger: '.dropdown-trigger',
    dropdownContent: '.dropdown-content',
    dropdownItem: '.dropdown-item',
    themeSwitcher: '.theme-switcher',
    themeOption: '.theme-option'
  },
  
  // Content filtering selectors
  contentFiltering: {
    filterWarning: '.content-filter-warning',
    filterDialog: '.content-filter-dialog',
    filterMessage: '.filter-message',
    acknowledgeButton: '.acknowledge-filter',
    reportButton: '.report-inappropriate'
  }
};