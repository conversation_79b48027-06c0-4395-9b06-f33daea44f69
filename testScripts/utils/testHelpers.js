/**
 * Helper functions for the test suite
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const selectors = require('./selectors');

module.exports = {
  /**
   * Wait for a specific amount of time
   * @param {number} ms - The number of milliseconds to wait
   * @returns {Promise} - Promise that resolves after the specified time
   */
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  /**
   * Generate a random string of specified length
   * @param {number} length - The length of the string to generate
   * @returns {string} - A random string
   */
  randomString: (length = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  /**
   * Generate a unique test username
   * @param {string} prefix - Optional prefix for the username
   * @returns {string} - A unique username
   */
  generateTestUsername: (prefix = 'testuser') => {
    return `${prefix}-${Date.now().toString(36)}-${Math.floor(Math.random() * 1000)}`;
  },
  
  /**
   * Helper to log in using the provided credentials
   * @param {Page} page - Puppeteer page object
   * @param {object} credentials - Object containing username and password
   * @param {object} options - Additional options
   * @returns {Promise<boolean>} - Whether login was successful
   */
  login: async (page, credentials, options = {}) => {
    const { username, password } = credentials;
    const timeout = options.timeout || 10000;
    
    try {
      // Navigate to login page if not already there
      if (!page.url().includes('login')) {
        await page.goto(`${options.baseUrl || 'http://localhost:3000'}/login`);
        await page.waitForSelector(selectors.auth.loginForm, { timeout });
      }
      
      // Fill in login form
      await page.type(selectors.auth.usernameInput, username);
      await page.type(selectors.auth.passwordInput, password);
      
      // Submit form
      await Promise.all([
        page.click(selectors.auth.loginButton),
        page.waitForNavigation({ timeout })
      ]);
      
      // Check if login was successful by looking for a known element
      return await page.evaluate(() => {
        return !document.location.href.includes('/login');
      });
    } catch (error) {
      console.error(`Login error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Helper to log out the current user
   * @param {Page} page - Puppeteer page object
   * @param {object} options - Additional options
   * @returns {Promise<boolean>} - Whether logout was successful
   */
  logout: async (page, options = {}) => {
    const timeout = options.timeout || 10000;
    
    try {
      // Click the user menu to show the logout button
      if (await page.$(selectors.auth.userMenu) !== null) {
        await page.click(selectors.auth.userMenu);
        await page.waitForSelector(selectors.auth.logoutButton, { timeout });
      }
      
      // Click logout
      await Promise.all([
        page.click(selectors.auth.logoutButton),
        page.waitForNavigation({ timeout })
      ]);
      
      // Check if logout was successful
      return await page.evaluate(() => {
        return document.location.href.includes('/login');
      });
    } catch (error) {
      console.error(`Logout error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Register a new user with random credentials
   * @param {Page} page - Puppeteer page object
   * @param {object} options - Additional options
   * @returns {Promise<object>} - The credentials of the registered user
   */
  registerUser: async (page, options = {}) => {
    const timeout = options.timeout || 10000;
    const username = options.username || `testuser-${Date.now().toString(36)}`;
    const password = options.password || 'password123';
    
    try {
      // Navigate to register page if not already there
      if (!page.url().includes('register')) {
        await page.goto(`${options.baseUrl || 'http://localhost:3000'}/login`);
        
        // Look for register tab or register link
        const registerLinkSelector = 'a[href="/register"], button:has-text("Register")';
        await page.waitForSelector(registerLinkSelector, { timeout });
        await page.click(registerLinkSelector);
        
        await page.waitForSelector(selectors.auth.registerForm, { timeout });
      }
      
      // Fill in registration form
      await page.type(selectors.auth.usernameInput, username);
      await page.type(selectors.auth.passwordInput, password);
      
      if (await page.$(selectors.auth.confirmPasswordInput) !== null) {
        await page.type(selectors.auth.confirmPasswordInput, password);
      }
      
      // Submit form
      await Promise.all([
        page.click(selectors.auth.registerButton),
        page.waitForNavigation({ timeout })
      ]);
      
      const credentials = { username, password };
      
      // Check if registration was successful
      const success = await page.evaluate(() => {
        return !document.location.href.includes('/register');
      });
      
      return { success, credentials };
    } catch (error) {
      console.error(`Registration error: ${error.message}`);
      return { success: false, credentials: { username, password } };
    }
  },
  
  /**
   * Create a new forum thread
   * @param {Page} page - Puppeteer page object
   * @param {object} threadData - Thread data to create
   * @param {object} options - Additional options
   * @returns {Promise<object>} - The created thread data
   */
  createThread: async (page, threadData, options = {}) => {
    const timeout = options.timeout || 10000;
    const title = threadData.title || `Test Thread ${Date.now()}`;
    const content = threadData.content || 'This is a test thread content.';
    const category = threadData.category || 'general';
    
    try {
      // Navigate to new thread page
      await page.goto(`${options.baseUrl || 'http://localhost:3000'}/forum/new`);
      await page.waitForSelector(selectors.forum.threadForm, { timeout });
      
      // Fill in thread form
      await page.type(selectors.forum.titleInput, title);
      
      // Handle rich text editor or plain textarea
      if (await page.$(selectors.forum.contentEditor) !== null) {
        // For rich text editor
        await page.click(selectors.forum.contentEditor);
        await page.keyboard.type(content);
      } else {
        // For plain textarea
        await page.type('textarea[name="content"]', content);
      }
      
      // Select category if dropdown exists
      if (await page.$(selectors.forum.categorySelect) !== null) {
        await page.select(selectors.forum.categorySelect, category);
      }
      
      // Submit form
      await Promise.all([
        page.click(selectors.forum.submitThreadButton),
        page.waitForNavigation({ timeout })
      ]);
      
      // Extract thread ID from URL
      const threadId = await page.evaluate(() => {
        const match = document.location.href.match(/thread\/(\d+)/);
        return match ? parseInt(match[1]) : null;
      });
      
      return {
        success: !!threadId,
        threadId,
        title,
        content,
        category
      };
    } catch (error) {
      console.error(`Create thread error: ${error.message}`);
      return { success: false, title, content, category };
    }
  },
  
  /**
   * Create a reply to a thread
   * @param {Page} page - Puppeteer page object
   * @param {string} content - Reply content
   * @param {object} options - Additional options
   * @returns {Promise<object>} - The created reply data
   */
  createReply: async (page, content, options = {}) => {
    const timeout = options.timeout || 10000;
    const replyContent = content || `Test reply ${Date.now()}`;
    
    try {
      // Wait for reply form to be visible
      await page.waitForSelector(selectors.forum.replyForm, { timeout });
      
      // Fill in reply content
      if (await page.$(selectors.forum.replyContentEditor) !== null) {
        // For rich text editor
        await page.click(selectors.forum.replyContentEditor);
        await page.keyboard.type(replyContent);
      } else {
        // For plain textarea
        await page.type('textarea[name="content"]', replyContent);
      }
      
      // Submit reply
      await page.click(selectors.forum.submitReplyButton);
      
      // Wait for reply to appear
      await page.waitForFunction(
        (content) => {
          const replies = document.querySelectorAll('.reply-content');
          return Array.from(replies).some(el => el.textContent.includes(content));
        },
        { timeout },
        replyContent
      );
      
      return {
        success: true,
        content: replyContent
      };
    } catch (error) {
      console.error(`Create reply error: ${error.message}`);
      return { success: false, content: replyContent };
    }
  },
  
  /**
   * Extract text content from an element
   * @param {Page} page - Puppeteer page object
   * @param {string} selector - Element selector
   * @returns {Promise<string>} - The text content
   */
  getTextContent: async (page, selector) => {
    try {
      return await page.$eval(selector, el => el.textContent.trim());
    } catch (error) {
      console.error(`Get text content error: ${error.message}`);
      return '';
    }
  },
  
  /**
   * Check if an element exists on the page
   * @param {Page} page - Puppeteer page object
   * @param {string} selector - Element selector
   * @returns {Promise<boolean>} - Whether the element exists
   */
  elementExists: async (page, selector) => {
    return (await page.$(selector)) !== null;
  },
  
  /**
   * Wait for text to appear on the page
   * @param {Page} page - Puppeteer page object
   * @param {string} text - Text to wait for
   * @param {object} options - Additional options
   * @returns {Promise<boolean>} - Whether the text was found
   */
  waitForText: async (page, text, options = {}) => {
    const timeout = options.timeout || 10000;
    const selector = options.selector || 'body';
    
    try {
      await page.waitForFunction(
        (text, selector) => {
          const element = document.querySelector(selector);
          return element && element.textContent.includes(text);
        },
        { timeout },
        text,
        selector
      );
      return true;
    } catch (error) {
      console.error(`Wait for text error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Save a screenshot to the specified path
   * @param {Page} page - Puppeteer page object
   * @param {string} name - Screenshot name
   * @param {string} dir - Directory to save to
   * @returns {Promise<string>} - Path to the saved screenshot
   */
  takeScreenshot: async (page, name, dir = './test-screenshots') => {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      const timestamp = Date.now();
      const filePath = path.join(dir, `${name}-${timestamp}.png`);
      await page.screenshot({ path: filePath, fullPage: true });
      return filePath;
    } catch (error) {
      console.error(`Screenshot error: ${error.message}`);
      return null;
    }
  },
  
  /**
   * Check if a user is logged in
   * @param {Page} page - Puppeteer page object
   * @returns {Promise<boolean>} - Whether the user is logged in
   */
  isLoggedIn: async (page) => {
    try {
      return await page.evaluate(() => {
        return !!document.querySelector('.user-menu, .logout-button, .user-avatar');
      });
    } catch (error) {
      console.error(`Is logged in error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Get the currently logged in username
   * @param {Page} page - Puppeteer page object
   * @returns {Promise<string|null>} - The username or null if not logged in
   */
  getLoggedInUsername: async (page) => {
    try {
      return await page.evaluate(() => {
        const userElement = document.querySelector('.user-menu, .username');
        return userElement ? userElement.textContent.trim() : null;
      });
    } catch (error) {
      console.error(`Get username error: ${error.message}`);
      return null;
    }
  },
  
  /**
   * Navigate to a specific page and wait for it to load
   * @param {Page} page - Puppeteer page object
   * @param {string} route - Route to navigate to
   * @param {object} options - Additional options
   * @returns {Promise<boolean>} - Whether navigation was successful
   */
  navigateTo: async (page, route, options = {}) => {
    const baseUrl = options.baseUrl || 'http://localhost:3000';
    const timeout = options.timeout || 10000;
    const waitForSelector = options.waitForSelector;
    
    try {
      await page.goto(`${baseUrl}${route}`);
      
      if (waitForSelector) {
        await page.waitForSelector(waitForSelector, { timeout });
      } else {
        await page.waitForFunction(
          () => document.readyState === 'complete',
          { timeout }
        );
      }
      
      return true;
    } catch (error) {
      console.error(`Navigation error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Check if a user has admin privileges
   * @param {Page} page - Puppeteer page object
   * @returns {Promise<boolean>} - Whether the user has admin privileges
   */
  isAdmin: async (page) => {
    try {
      return await page.evaluate(() => {
        return !!document.querySelector('a[href="/admin"]');
      });
    } catch (error) {
      console.error(`Is admin error: ${error.message}`);
      return false;
    }
  },
  
  /**
   * Intercept network requests to mock responses or track API calls
   * @param {Page} page - Puppeteer page object
   * @param {Function} requestHandler - Function to handle requests
   * @returns {Promise<void>}
   */
  interceptRequests: async (page, requestHandler) => {
    await page.setRequestInterception(true);
    page.on('request', requestHandler);
  },
  
  /**
   * Stop intercepting network requests
   * @param {Page} page - Puppeteer page object
   * @param {Function} requestHandler - Function that was handling requests
   * @returns {Promise<void>}
   */
  stopInterceptingRequests: async (page, requestHandler) => {
    page.removeListener('request', requestHandler);
    await page.setRequestInterception(false);
  },
  
  /**
   * Get performance metrics for the page
   * @param {Page} page - Puppeteer page object
   * @returns {Promise<object>} - Performance metrics
   */
  getPerformanceMetrics: async (page) => {
    try {
      // First, check if there are any client-side performance metrics
      const clientMetrics = await page.evaluate(() => {
        if (window.performance && window.performance.timing) {
          const timing = window.performance.timing;
          return {
            navigationStart: timing.navigationStart,
            connectEnd: timing.connectEnd,
            responseStart: timing.responseStart,
            responseEnd: timing.responseEnd,
            domComplete: timing.domComplete,
            loadEventEnd: timing.loadEventEnd,
            
            // Derived metrics
            ttfb: timing.responseStart - timing.navigationStart,
            domLoad: timing.domComplete - timing.navigationStart,
            fullLoad: timing.loadEventEnd - timing.navigationStart
          };
        }
        return null;
      });
      
      // Then get Puppeteer's built-in metrics
      const puppeteerMetrics = await page.metrics();
      
      return {
        client: clientMetrics,
        puppeteer: puppeteerMetrics
      };
    } catch (error) {
      console.error(`Performance metrics error: ${error.message}`);
      return null;
    }
  },
  
  /**
   * Send a direct message to another user
   * @param {Page} page - Puppeteer page object
   * @param {string} recipientUsername - Username of the recipient
   * @param {string} messageContent - Content of the message
   * @param {boolean} encrypted - Whether to encrypt the message
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Result of the send operation
   */
  sendDirectMessage: async (page, recipientUsername, messageContent, encrypted = false, options = {}) => {
    const timeout = options.timeout || 10000;
    
    try {
      // Navigate to messages page
      await module.exports.navigateTo(page, '/messages', { 
        waitForSelector: selectors.messages.messagesPage, 
        ...options 
      });
      
      // Select recipient from user list
      const userSelector = `.user-item:has-text("${recipientUsername}")`;
      await page.waitForSelector(userSelector, { timeout });
      await page.click(userSelector);
      
      // Wait for message form to appear
      await page.waitForSelector(selectors.messages.messageForm, { timeout });
      
      // Toggle encryption if needed
      if (encrypted) {
        if (await module.exports.elementExists(page, selectors.messages.encryptionToggle)) {
          const currentState = await page.$eval(
            selectors.messages.encryptionToggle,
            el => el.getAttribute('aria-checked') === 'true'
          );
          
          if (currentState !== encrypted) {
            await page.click(selectors.messages.encryptionToggle);
            await module.exports.sleep(500); // Wait for toggle animation
          }
        }
      }
      
      // Type message
      await page.type(selectors.messages.messageInput, messageContent);
      
      // Send message
      await page.click(selectors.messages.sendButton);
      
      // Check if message was sent successfully
      const success = await page.waitForFunction(
        content => {
          const messages = document.querySelectorAll('.message-outgoing .message-content');
          return Array.from(messages).some(el => el.textContent.includes(content));
        },
        { timeout },
        messageContent
      ).then(() => true).catch(() => false);
      
      return {
        success,
        encrypted,
        content: messageContent,
        recipient: recipientUsername
      };
    } catch (error) {
      console.error(`Send message error: ${error.message}`);
      return {
        success: false,
        encrypted,
        content: messageContent,
        recipient: recipientUsername,
        error: error.message
      };
    }
  },
  
  /**
   * Send a message in the AI chat
   * @param {Page} page - Puppeteer page object
   * @param {string} message - Message to send
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Result of the AI interaction
   */
  sendAIMessage: async (page, message, options = {}) => {
    const timeout = options.aiTimeout || options.timeout || 45000; // AI can take longer
    
    try {
      // Navigate to AI chat page
      await module.exports.navigateTo(page, '/ai', { 
        waitForSelector: selectors.ai.aiPage, 
        ...options 
      });
      
      // Type message
      await page.type(selectors.ai.chatInput, message);
      
      // Send message
      await page.click(selectors.ai.sendButton);
      
      // Wait for user message to appear
      await page.waitForFunction(
        content => {
          const messages = document.querySelectorAll('.user-message');
          return Array.from(messages).some(el => el.textContent.includes(content));
        },
        { timeout: timeout / 3 },
        message
      );
      
      // Wait for AI typing indicator
      await page.waitForSelector(selectors.ai.aiTypingIndicator, { timeout: timeout / 3 })
        .catch(() => console.log('No AI typing indicator found')); // Optional
      
      // Wait for AI response
      await page.waitForSelector(selectors.ai.aiMessage, { timeout });
      
      // Extract the AI response
      const aiResponse = await page.evaluate(() => {
        const responses = document.querySelectorAll('.ai-message');
        return responses.length > 0 ? responses[responses.length - 1].textContent : null;
      });
      
      return {
        success: !!aiResponse,
        userMessage: message,
        aiResponse
      };
    } catch (error) {
      console.error(`AI chat error: ${error.message}`);
      return {
        success: false,
        userMessage: message,
        error: error.message
      };
    }
  },
  
  /**
   * Vote on a thread
   * @param {Page} page - Puppeteer page object
   * @param {string} direction - 'up' or 'down'
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Result of the vote operation
   */
  voteOnThread: async (page, direction, options = {}) => {
    const timeout = options.timeout || 10000;
    
    try {
      // Find the vote button
      const voteButtonSelector = direction === 'up' 
        ? selectors.forum.upvoteButton 
        : selectors.forum.downvoteButton;
      
      await page.waitForSelector(voteButtonSelector, { timeout });
      
      // Get current vote count
      const voteCountBefore = await module.exports.getTextContent(page, selectors.forum.voteCount);
      const numVotesBefore = parseInt(voteCountBefore) || 0;
      
      // Click vote button
      await page.click(voteButtonSelector);
      
      // Wait for vote to be registered (may be visual change or DOM change)
      await page.waitForFunction(
        (selector, prevCount) => {
          const countEl = document.querySelector(selector);
          if (!countEl) return false;
          const newCount = parseInt(countEl.textContent.trim()) || 0;
          return newCount !== prevCount;
        },
        { timeout },
        selectors.forum.voteCount,
        numVotesBefore
      ).catch(() => {}); // Ignore errors, maybe count didn't change
      
      // Get new vote count
      const voteCountAfter = await module.exports.getTextContent(page, selectors.forum.voteCount);
      const numVotesAfter = parseInt(voteCountAfter) || 0;
      
      const expectedDirection = direction === 'up' ? 1 : -1;
      const actualDirection = Math.sign(numVotesAfter - numVotesBefore);
      
      return {
        success: actualDirection === expectedDirection || actualDirection === 0, // May be toggling off
        direction,
        voteBefore: numVotesBefore,
        voteAfter: numVotesAfter
      };
    } catch (error) {
      console.error(`Vote error: ${error.message}`);
      return {
        success: false,
        direction,
        error: error.message
      };
    }
  },
  
  /**
   * Upload a file attachment in AI chat
   * @param {Page} page - Puppeteer page object
   * @param {string} filePath - Path to the file to upload
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Result of the upload operation
   */
  uploadAttachment: async (page, filePath, options = {}) => {
    const timeout = options.timeout || 10000;
    
    try {
      // First check if upload button exists
      await page.waitForSelector(selectors.ai.uploadAttachmentButton, { timeout });
      
      // Set up file input listener
      const [fileChooser] = await Promise.all([
        page.waitForFileChooser(),
        page.click(selectors.ai.uploadAttachmentButton)
      ]);
      
      // Upload file
      await fileChooser.accept([filePath]);
      
      // Wait for attachment preview
      await page.waitForSelector(selectors.ai.attachmentPreview, { timeout });
      
      return {
        success: true,
        filePath
      };
    } catch (error) {
      console.error(`Upload attachment error: ${error.message}`);
      return {
        success: false,
        filePath,
        error: error.message
      };
    }
  },
  
  /**
   * Check for responsive layout at different viewport sizes
   * @param {Page} page - Puppeteer page object
   * @param {Array<{width: number, height: number}>} viewportSizes - List of viewport sizes to test
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Results of responsive tests
   */
  checkResponsiveLayout: async (page, viewportSizes, options = {}) => {
    const timeout = options.timeout || 10000;
    const results = [];
    
    // Store original viewport
    const originalViewport = page.viewport();
    
    try {
      for (const size of viewportSizes) {
        // Set new viewport size
        await page.setViewport(size);
        
        // Wait for layout to adjust
        await module.exports.sleep(500);
        
        // Take screenshot if enabled
        let screenshotPath = null;
        if (options.screenshots) {
          screenshotPath = await module.exports.takeScreenshot(
            page, 
            `responsive-${size.width}x${size.height}`,
            options.screenshotDir
          );
        }
        
        // Check for mobile menu button (common indicator of responsive design)
        const hasMobileMenu = await module.exports.elementExists(page, selectors.navigation.mobileMenuButton);
        
        // Check for horizontally overflowing elements
        const hasOverflow = await page.evaluate(() => {
          const docWidth = document.documentElement.clientWidth;
          const overflowingElements = Array.from(document.querySelectorAll('*')).filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.left < 0 || rect.right > docWidth;
          });
          return overflowingElements.length > 0;
        });
        
        results.push({
          viewport: size,
          hasMobileMenu,
          hasOverflow,
          screenshotPath
        });
      }
      
      // Restore original viewport
      await page.setViewport(originalViewport);
      
      return {
        success: true,
        results
      };
    } catch (error) {
      console.error(`Responsive test error: ${error.message}`);
      
      // Restore original viewport
      await page.setViewport(originalViewport);
      
      return {
        success: false,
        error: error.message,
        results
      };
    }
  },
  
  /**
   * Create a test AI chat session
   * @param {Page} page - Puppeteer page object
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Result of the session creation
   */
  createAIChatSession: async (page, options = {}) => {
    const timeout = options.timeout || 10000;
    const sessionName = options.sessionName || `Test Session ${Date.now()}`;
    
    try {
      // Navigate to AI chat page
      await module.exports.navigateTo(page, '/ai', { 
        waitForSelector: selectors.ai.aiPage, 
        ...options 
      });
      
      // Click new session button
      await page.waitForSelector(selectors.ai.newSessionButton, { timeout });
      await page.click(selectors.ai.newSessionButton);
      
      // Wait for new session to be created (depends on your UI)
      await page.waitForFunction(
        () => {
          // May need to adapt this logic based on your application's behavior
          const newSession = document.querySelector('.ai-session.active');
          return newSession !== null;
        },
        { timeout }
      );
      
      // Rename session if the app supports it
      if (options.rename && await module.exports.elementExists(page, '.session-rename-button')) {
        await page.click('.session-rename-button');
        await page.type('.session-title-input', sessionName);
        await page.keyboard.press('Enter');
      }
      
      return {
        success: true,
        sessionName
      };
    } catch (error) {
      console.error(`Create AI session error: ${error.message}`);
      return {
        success: false,
        sessionName,
        error: error.message
      };
    }
  },
  
  /**
   * Test the content filtering functionality
   * @param {Page} page - Puppeteer page object
   * @param {string} contentWithViolation - Content that should trigger the filter
   * @param {object} options - Additional options
   * @returns {Promise<object>} - Result of the content filtering test
   */
  testContentFiltering: async (page, contentWithViolation, options = {}) => {
    const timeout = options.timeout || 10000;
    
    try {
      // Navigate to thread creation page
      await module.exports.navigateTo(page, '/forum/new', { 
        waitForSelector: selectors.forum.threadForm, 
        ...options 
      });
      
      // Fill in thread form
      await page.type(selectors.forum.titleInput, 'Test Content Filtering');
      
      // Handle rich text editor or plain textarea
      if (await module.exports.elementExists(page, selectors.forum.contentEditor)) {
        // For rich text editor
        await page.click(selectors.forum.contentEditor);
        await page.keyboard.type(contentWithViolation);
      } else {
        // For plain textarea
        await page.type('textarea[name="content"]', contentWithViolation);
      }
      
      // Submit form
      await page.click(selectors.forum.submitThreadButton);
      
      // Check for content filter dialog or error message
      const hasFilterDialog = await Promise.race([
        page.waitForSelector(selectors.ui.contentFilterDialog, { timeout })
          .then(() => true)
          .catch(() => false),
        page.waitForSelector(selectors.ui.errorMessage, { timeout })
          .then(() => true)
          .catch(() => false)
      ]);
      
      return {
        success: true, // The test worked, regardless of filtering outcome
        filtered: hasFilterDialog,
        content: contentWithViolation
      };
    } catch (error) {
      console.error(`Content filtering test error: ${error.message}`);
      return {
        success: false,
        content: contentWithViolation,
        error: error.message
      };
    }
  }
};