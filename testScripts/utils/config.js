/**
 * Test configuration utilities
 */
const fs = require('fs');
const path = require('path');

// Default configuration
const defaultConfig = {
  // Browser settings
  headless: true,
  slowMo: 0,
  
  // Application settings
  baseUrl: 'http://localhost:3000',
  
  // Test settings
  retries: 2,
  captureScreenshots: true,
  screenshotDir: './test-screenshots',
  outputDir: './test-results',
  
  // Timeout settings (ms)
  timeout: {
    navigation: 30000,
    element: 10000,
    api: 15000,
    ai: 45000
  },
  
  // Test credentials
  credentials: {
    admin: {
      username: 'admin',
      password: 'admin123'
    },
    user: {
      username: 'testuser',
      password: 'password123'
    }
  }
};

/**
 * Load configuration from environment variables
 * @returns {Object} Environment-based configuration
 */
function loadEnvConfig() {
  try {
    // Try to load dotenv if available
    try {
      require('dotenv').config();
    } catch (e) {
      console.error('Error loading dotenv:', e.message);
    }
    
    const config = {};
    
    // Browser settings
    if (process.env.TEST_HEADLESS !== undefined) {
      config.headless = process.env.TEST_HEADLESS === 'true';
    }
    
    // Application settings
    if (process.env.TEST_BASE_URL) {
      config.baseUrl = process.env.TEST_BASE_URL;
    }
    
    // Test settings
    if (process.env.TEST_RETRIES) {
      config.retries = parseInt(process.env.TEST_RETRIES, 10);
    }
    
    if (process.env.TEST_SCREENSHOTS !== undefined) {
      config.captureScreenshots = process.env.TEST_SCREENSHOTS === 'true';
    }
    
    if (process.env.TEST_SCREENSHOT_DIR) {
      config.screenshotDir = process.env.TEST_SCREENSHOT_DIR;
    }
    
    if (process.env.TEST_OUTPUT_DIR) {
      config.outputDir = process.env.TEST_OUTPUT_DIR;
    }
    
    // Timeout settings
    if (process.env.TEST_TIMEOUT_NAVIGATION) {
      config.timeout = config.timeout || {};
      config.timeout.navigation = parseInt(process.env.TEST_TIMEOUT_NAVIGATION, 10);
    }
    
    if (process.env.TEST_TIMEOUT_ELEMENT) {
      config.timeout = config.timeout || {};
      config.timeout.element = parseInt(process.env.TEST_TIMEOUT_ELEMENT, 10);
    }
    
    if (process.env.TEST_TIMEOUT_API) {
      config.timeout = config.timeout || {};
      config.timeout.api = parseInt(process.env.TEST_TIMEOUT_API, 10);
    }
    
    if (process.env.TEST_TIMEOUT_AI) {
      config.timeout = config.timeout || {};
      config.timeout.ai = parseInt(process.env.TEST_TIMEOUT_AI, 10);
    }
    
    // Credentials (if specified in env)
    if (process.env.TEST_ADMIN_USERNAME && process.env.TEST_ADMIN_PASSWORD) {
      config.credentials = config.credentials || {};
      config.credentials.admin = {
        username: process.env.TEST_ADMIN_USERNAME,
        password: process.env.TEST_ADMIN_PASSWORD
      };
    }
    
    if (process.env.TEST_USER_USERNAME && process.env.TEST_USER_PASSWORD) {
      config.credentials = config.credentials || {};
      config.credentials.user = {
        username: process.env.TEST_USER_USERNAME,
        password: process.env.TEST_USER_PASSWORD
      };
    }
    
    return config;
  } catch (error) {
    console.error('Error loading environment configuration:', error);
    return {};
  }
}

/**
 * Get configuration with optional overrides
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} - Final configuration
 */
function getConfig(overrides = {}) {
  // Start with default config
  const baseConfig = { ...defaultConfig };
  
  // Load environment configuration
  const envConfig = loadEnvConfig();
  
  // Merge configs with priority: defaults < environment < explicit overrides
  const mergedConfig = {
    ...baseConfig,
    ...envConfig,
    ...overrides,
    // For nested objects like timeout, we need to merge separately
    timeout: {
      ...baseConfig.timeout,
      ...(envConfig.timeout || {}),
      ...(overrides.timeout || {})
    },
    credentials: {
      ...baseConfig.credentials,
      ...(envConfig.credentials || {}),
      ...(overrides.credentials || {})
    }
  };
  
  return mergedConfig;
}

module.exports = { getConfig, defaultConfig };