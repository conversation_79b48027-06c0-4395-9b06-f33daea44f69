{"baseUrl": "http://localhost:3000", "headless": false, "slowMo": 100, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "retries": 2, "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "viewportSize": {"width": 1280, "height": 800}, "users": {"admin": {"username": "admin", "password": "admin123"}, "standard": {"username": "testuser", "password": "test123"}, "recipient": {"username": "recipient", "password": "recipient123"}}, "testFilter": ["login", "logout", "forumThread"]}