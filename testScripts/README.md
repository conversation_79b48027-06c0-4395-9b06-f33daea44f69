# Vintage Forum Automated Test Suite

This comprehensive test suite provides automated testing for the Vintage Forum application, covering all major features and functionality including specialized tests for advanced features.

## Features

- **Complete End-to-End Testing**: Tests all aspects of the application from authentication to forum interactions
- **Specialized Feature Testing**: In-depth tests for AI chat, encrypted messaging, and other advanced features
- **Automatic Screenshot Capture**: Takes screenshots on test failures for easier debugging
- **HTML and JSON Reports**: Generates detailed reports of test results
- **Configurable Testing**: Run specific test groups or customize testing parameters with environment variables
- **Retry Mechanism**: Automatically retries failed tests to minimize flakiness
- **Performance Testing**: Includes tests for page load performance and responsive layout
- **Centralized Selectors**: All UI selectors are centralized for easier maintenance
- **Reusable Test Helpers**: Common test operations extracted into reusable helper functions
- **Multi-Browser Testing**: Support for testing interactions between multiple users

## Prerequisites

- Node.js >= 14.x
- npm >= 6.x
- Running instance of the Vintage Forum application at http://localhost:3000 (configurable)

## Installation

```bash
cd testScripts
npm install
```

## Configuration

You can configure the test suite in multiple ways:

### 1. Environment Variables

Copy the `.env.example` file to `.env` and customize the values:

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 2. Command Line Arguments

When using the CLI runner (`runTests.js`):

```bash
node runTests.js --headless=false --url=http://myserver:3000 --retries=3
```

### 3. Config File

Create a JSON config file and specify it with the `--config` option:

```bash
node runTests.js --config=my-config.json
```

## Running Tests

### Standard Tests

```bash
# Run all standard tests
npm test
# or
npm run test:all

# Run standard tests with browser visible
npm run test:visible
# or
npm run test:all-visible
```

### Test Specific Features

```bash
# Authentication tests
npm run test:auth

# Forum functionality tests
npm run test:forum

# Messaging tests (direct and public)
npm run test:messaging

# AI integration basic tests
npm run test:ai

# Admin functionality tests
npm run test:admin

# API tests
npm run test:api

# UI and performance tests
npm run test:ui
```

### Specialized Tests

```bash
# Run all specialized tests
npm run test:specialized

# Run all specialized tests with browser visible
npm run test:specialized-visible

# Run in-depth AI chat tests
npm run test:ai-deep

# Run encrypted messaging tests
npm run test:encryption
```

### View Test Reports

```bash
# Open the latest HTML report
npm run report
```

## Project Structure

```
testScripts/
├── advancedTestScript.js  # Main test script with standard tests
├── runTests.js            # CLI runner for standard tests
├── runAllSpecialized.js   # Runner for specialized test suites
├── package.json           # Project configuration and scripts
├── .env.example           # Example environment configuration
├── sample-config.json     # Example JSON configuration
├── README.md              # Documentation
├── specialized/           # Specialized test suites
│   ├── aiChatTests.js     # In-depth AI chat tests
│   └── encryptedMessagingTests.js # Encrypted messaging tests
└── utils/                # Shared utilities
    ├── config.js         # Configuration loader
    ├── selectors.js      # Centralized CSS selectors
    └── testHelpers.js    # Common test operations
```

## Test Reports

Tests generate both HTML and JSON reports:

1. **Standard Tests**: Reports saved in `test-results/` directory
2. **Specialized Tests**: Reports saved in `test-results/specialized/` directory

Reports include:
- Test status (pass/fail)
- Test duration
- Error details
- Screenshots of failures (when enabled)
- Performance metrics (for performance tests)

## Specialized Test Features

### AI Chat Tests

Tests the AI chat functionality in depth, including:
- Basic chat functionality
- Streaming responses
- Session management
- Context retention
- Error handling

### Encrypted Messaging Tests

Tests the encrypted messaging functionality, including:
- Encryption availability and toggling
- Encrypted message sending
- Public key exchange
- Two-browser encryption test (advanced)

## Extending Tests

### Adding New Standard Tests

1. Add a new test function in the `// ========== Test Definitions ==========` section of `advancedTestScript.js`
2. Add your test to the `allTests` object in the same file
3. Update `package.json` scripts if you want to run it independently

### Adding New Specialized Tests

1. Create a new file in the `specialized/` directory
2. Import and use utilities from the `utils/` directory
3. Add your test runner to `runAllSpecialized.js`
4. Add a script to `package.json` for running it independently

## Common Issues and Troubleshooting

- **Element Not Found Errors**: Check if selectors match your application's structure. Update them in `utils/selectors.js` if needed.
- **Timeout Errors**: Increase timeout values in the CONFIG object or via environment variables.
- **Authentication Issues**: Verify test user credentials in the CONFIG or environment variables.
- **Puppeteer Installation Issues**: If you encounter issues with Puppeteer installation, check their documentation for platform-specific requirements.
- **Encryption Test Failures**: The two-browser encryption test requires both sender and recipient users to exist and have correct permissions.

## Advanced Usage

### Running on CI/CD

For CI/CD integration:

1. Set the `HEADLESS` environment variable to `true`
2. Set appropriate timeouts for CI environment
3. Use JSON reports for programmatic analysis

Example for GitHub Actions:

```yaml
- name: Run tests
  run: cd testScripts && npm test
  env:
    HEADLESS: true
    NAVIGATION_TIMEOUT: 60000
    ELEMENT_TIMEOUT: 30000
```

### Customizing Browser Options

For advanced Puppeteer configuration, modify the browser launch options in each test file:

```javascript
browser = await puppeteer.launch({
  headless: config.headless,
  slowMo: config.slowMo,
  args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
  defaultViewport: null // Use window size instead
});
```