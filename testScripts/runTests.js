// runTests.js - CLI runner for the test suite
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const chalk = require('chalk');
const { getConfig } = require('./utils/config');
const { runTests } = require('./advancedTestScript');

// Get default config
const DEFAULT_CONFIG = getConfig();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('tests', {
    alias: 't',
    description: 'Comma-separated list of test names to run',
    type: 'string',
  })
  .option('headless', {
    alias: 'h',
    description: 'Run in headless mode',
    type: 'boolean',
    default: true,
  })
  .option('url', {
    alias: 'u',
    description: 'Base URL of the application to test',
    type: 'string',
    default: DEFAULT_CONFIG.baseUrl,
  })
  .option('retries', {
    alias: 'r',
    description: 'Number of times to retry failed tests',
    type: 'number',
    default: DEFAULT_CONFIG.retries,
  })
  .option('timeout', {
    description: 'Global timeout for operations in milliseconds',
    type: 'number',
  })
  .option('screenshots', {
    alias: 's',
    description: 'Capture screenshots on test failures',
    type: 'boolean',
    default: DEFAULT_CONFIG.captureScreenshots,
  })
  .option('slowMo', {
    description: 'Slow down browser operations by ms',
    type: 'number',
    default: DEFAULT_CONFIG.slowMo || 0,
  })
  .option('config', {
    alias: 'c',
    description: 'Path to custom configuration JSON file',
    type: 'string',
  })
  .help()
  .alias('help', 'info')
  .argv;

async function main() {
  console.log(chalk.blue('=== Vintage Forum Test Suite ==='));
  
  // Prepare configuration
  const config = { ...DEFAULT_CONFIG };
  
  // Override with custom config file if provided
  if (argv.config) {
    try {
      const fs = require('fs');
      const customConfig = JSON.parse(fs.readFileSync(argv.config, 'utf8'));
      Object.assign(config, customConfig);
      console.log(chalk.green(`Loaded custom configuration from ${argv.config}`));
    } catch (error) {
      console.error(chalk.red(`Error loading custom config: ${error.message}`));
      process.exit(1);
    }
  }
  
  // Override with command line arguments
  config.headless = argv.headless;
  config.baseUrl = argv.url;
  config.retries = argv.retries;
  config.captureScreenshots = argv.screenshots;
  config.slowMo = argv.slowMo;
  
  if (argv.timeout) {
    config.timeout = {
      navigation: argv.timeout,
      element: argv.timeout,
      api: argv.timeout,
    };
  }
  
  // Filter tests if specified
  if (argv.tests) {
    config.testFilter = argv.tests.split(',').map(t => t.trim());
    console.log(chalk.yellow(`Running only selected tests: ${config.testFilter.join(', ')}`));
  }
  
  // Display config
  console.log(chalk.gray('Configuration:'));
  console.log(chalk.gray(`  Base URL: ${config.baseUrl}`));
  console.log(chalk.gray(`  Headless: ${config.headless}`));
  console.log(chalk.gray(`  Retries: ${config.retries}`));
  
  // Run tests
  try {
    const results = await runTests(config);
    
    // Exit with appropriate code
    const failedTests = results.filter(r => r.status === 'fail').length;
    process.exit(failedTests > 0 ? 1 : 0);
  } catch (error) {
    console.error(chalk.red('Test runner encountered an error:'));
    console.error(error);
    process.exit(1);
  }
}

main();