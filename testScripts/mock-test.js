/**
 * Simple mock test script to validate the test runner
 */
const chalk = require('chalk');
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Run mock tests for framework testing
 * @param {Object} config - Configuration options
 * @returns {Array} - Test results
 */
async function runMockTests(config = {}) {
  console.log(chalk.blue('Running mock tests with config:'));
  console.log(config);
  
  // Mock test results
  const results = [
    {
      test: 'Mock Test 1: Basic Functionality',
      status: 'pass',
      duration: 123,
      details: 'This is a mock passing test'
    },
    {
      test: 'Mock Test 2: Data Processing',
      status: 'pass',
      duration: 75,
      details: 'Data processing functionality verified'
    },
    {
      test: 'Mock Test 3: Error Handling',
      status: 'fail',
      duration: 456,
      error: 'This is a mock error message',
      details: 'This is a mock failing test'
    }
  ];
  
  // Filter tests if specified
  let filteredResults = results;
  if (config.testFilter && Array.isArray(config.testFilter) && config.testFilter.length > 0) {
    console.log(chalk.yellow(`Filtering tests to: ${config.testFilter.join(', ')}`));
    filteredResults = results.filter(result => {
      const testName = result.test.toLowerCase();
      return config.testFilter.some(filter => testName.includes(filter.toLowerCase()));
    });
  }
  
  // Simulate test execution with logging
  console.log(chalk.cyan('\nRunning mock tests...\n'));
  await delay(300); // Simulate startup time
  
  for (const result of filteredResults) {
    await delay(100); // Simulate test execution time
    if (result.status === 'pass') {
      console.log(chalk.green(`✓ ${result.test} passed (${result.duration}ms)`));
    } else {
      console.log(chalk.red(`✗ ${result.test} failed (${result.duration}ms)`));
      console.log(chalk.gray(`  ${result.error}`));
    }
  }
  
  // Simulate teardown time
  await delay(200);
  
  return filteredResults;
}

module.exports = { runMockTests };