
# Crow-AI Database Export Guide

## Database Schema Information

Your Crow-AI forum uses PostgreSQL with the following tables and structure:

### Core Tables:
1. **users** - User accounts, authentication, encryption keys
2. **threads** - Forum posts with categories and content
3. **replies** - Responses to forum threads
4. **votes** - Upvote/downvote system for threads
5. **messages** - Direct messaging (supports E2EE)
6. **ai_chats** - AI conversation history
7. **ai_chat_sessions** - AI chat session groupings
8. **chat_room_messages** - Public chat room messages
9. **user_events** - Audit log of user actions

### Key Features:
- End-to-End Encryption support
- Content moderation/filtering
- Admin system with suspension/ban capabilities
- Session management
- Key rotation for encryption

## Method 1: Full Database Dump (Recommended)

### Using pgAdmin (GUI Method):
1. Open pgAdmin 4
2. Connect to your PostgreSQL server
3. Right-click on your database name
4. Select "Backup..."
5. Choose these settings:
   - Format: Custom
   - Compression: 9
   - Include: Data + Schema
   - Filename: `crow_ai_backup_YYYY-MM-DD.backup`

### Using Command Line:
```bash
# Full database dump with schema and data
pg_dump -h localhost -U postgres -d crow_ai_db -f crow_ai_full_backup.sql

# Custom format (smaller, faster restore)
pg_dump -h localhost -U postgres -d crow_ai_db -Fc -f crow_ai_backup.dump

# Include all settings and roles
pg_dumpall -h localhost -U postgres -f crow_ai_complete_backup.sql
```

## Method 2: Schema-Only Export

```bash
# Export only the database structure
pg_dump -h localhost -U postgres -d crow_ai_db --schema-only -f crow_ai_schema.sql
```

## Method 3: Data-Only Export

```bash
# Export only the data (no structure)
pg_dump -h localhost -U postgres -d crow_ai_db --data-only -f crow_ai_data.sql
```

## Method 4: Table-Specific Exports

```bash
# Export specific tables
pg_dump -h localhost -U postgres -d crow_ai_db -t users -t threads -t messages -f crow_ai_core_tables.sql

# Export with COPY format (faster for large datasets)
pg_dump -h localhost -U postgres -d crow_ai_db --column-inserts -f crow_ai_inserts.sql
```

## Restoring Your Database

### On New System:
1. Create new database:
```sql
CREATE DATABASE crow_ai_db;
```

2. Restore from backup:
```bash
# From SQL dump
psql -h localhost -U postgres -d crow_ai_db -f crow_ai_full_backup.sql

# From custom format
pg_restore -h localhost -U postgres -d crow_ai_db crow_ai_backup.dump
```

## Important Files to Copy

### Configuration Files:
- `.env` (contains DATABASE_URL and secrets)
- `drizzle.config.ts` (database configuration)
- `server/db.ts` (connection settings)

### Schema Definition:
- `shared/schema.ts` (complete database schema)

### Migration Files:
- `migrations/` folder (if exists)
- `migrate-e2ee.sql` (E2EE migration script)

## Environment Variables to Note

Make sure your `.env` file contains:
```
DATABASE_URL=postgresql://postgres:PASSWORD@localhost:5432/crow_ai_db
SESSION_SECRET=your-session-secret
OPENAI_API_KEY=your-openai-key (if used)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

## Database Schema Overview

### Users Table Structure:
- Basic auth (username, password, isAdmin)
- Encryption keys (publicKey, autoEncryptionEnabled)
- Security (suspendedUntil, banReason, bannedAt)
- Tracking (creationIp, lastLoginIp, lastLoginAt)

### Content Tables:
- **threads**: title, content, category, authorId, isDeleted
- **replies**: content, threadId, authorId
- **votes**: threadId, userId, isUpvote

### Messaging System:
- **messages**: content, nonce, isEncrypted, senderId, recipientId
- **chat_room_messages**: content, userId, roomId

### AI Integration:
- **ai_chat_sessions**: userId, title
- **ai_chats**: userId, sessionId, message, response, isDeepThinking

### Audit System:
- **user_events**: userId, type, details, ip, adminId, metadata

## Data Integrity Checks

Before export, verify your data:
```sql
-- Check table counts
SELECT 
  schemaname,
  tablename,
  n_tup_ins as "inserts",
  n_tup_upd as "updates",
  n_tup_del as "deletes"
FROM pg_stat_user_tables;

-- Check for any corrupted data
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM threads WHERE is_deleted = false;
SELECT COUNT(*) FROM messages;
```

## Security Considerations

1. **Backup Encryption**: Consider encrypting your backup files
2. **User Passwords**: Already hashed in database
3. **Private Keys**: Not stored in database (client-side only)
4. **Session Data**: Stored in sessions table

## Quick Backup Script

Save this as `backup.sh`:
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="crow_ai_db"
BACKUP_DIR="./backups"

mkdir -p $BACKUP_DIR

echo "Creating full backup..."
pg_dump -h localhost -U postgres -d $DB_NAME -Fc -f "$BACKUP_DIR/crow_ai_$DATE.dump"

echo "Creating SQL backup..."
pg_dump -h localhost -U postgres -d $DB_NAME -f "$BACKUP_DIR/crow_ai_$DATE.sql"

echo "Backup completed: $BACKUP_DIR/crow_ai_$DATE.*"
```

## Verification After Restore

1. Check all tables exist:
```sql
\dt
```

2. Verify data counts match:
```sql
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM threads;
SELECT COUNT(*) FROM messages;
```

3. Test application connectivity
4. Verify admin user exists
5. Test E2EE functionality

Your Crow-AI database contains all user data, encryption keys, forum content, messages, and configuration. The backup will preserve everything needed for a complete restoration.
