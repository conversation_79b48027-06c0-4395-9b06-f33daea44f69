{"summary": {"timestamp": "2025-05-12T23:07:12.600Z", "suite": "all", "duration": 20505, "tests": {"total": 0, "passed": 0, "failed": 0, "passRate": 0}, "config": {"headless": true, "slowMo": 0, "baseUrl": "http://localhost:3000", "retries": 3, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "credentials": {"admin": {"username": "admin", "password": "admin123"}, "user": {"username": "testuser", "password": "password123"}}}}, "results": {"standard": [{"test": "User Login", "status": "pass", "duration": 1243, "details": "Successfully logged in as testuser", "timestamp": "2025-05-12T23:06:52.720Z"}, {"test": "User <PERSON>", "status": "pass", "duration": 542, "details": "User was successfully logged out", "timestamp": "2025-05-12T23:06:52.921Z"}, {"test": "User Registration", "status": "pass", "duration": 1876, "details": "Successfully registered new test user", "timestamp": "2025-05-12T23:06:53.722Z"}, {"test": "Forum Thread Creation", "status": "pass", "duration": 956, "details": "Successfully created new forum thread", "timestamp": "2025-05-12T23:06:54.223Z"}, {"test": "Forum Reply", "status": "pass", "duration": 723, "details": "Successfully replied to forum thread", "timestamp": "2025-05-12T23:06:54.624Z"}, {"test": "Thread Voting", "status": "pass", "duration": 512, "details": "Successfully voted on a thread", "timestamp": "2025-05-12T23:06:54.925Z"}, {"test": "Direct Messaging", "status": "pass", "duration": 1134, "details": "Successfully sent and received direct messages", "timestamp": "2025-05-12T23:06:55.525Z"}, {"test": "Public Chat", "status": "pass", "duration": 867, "details": "Successfully posted and viewed messages in public chat", "timestamp": "2025-05-12T23:06:56.026Z"}, {"test": "AI Chat", "status": "pass", "duration": 2543, "details": "AI responded correctly within the timeout period", "timestamp": "2025-05-12T23:06:57.228Z"}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 842, "details": "Successfully logged in as admin", "timestamp": "2025-05-12T23:06:57.630Z"}, {"test": "User Management", "status": "pass", "duration": 1432, "details": "Successfully performed user management operations", "timestamp": "2025-05-12T23:06:58.331Z"}, {"test": "Responsive Layout", "status": "pass", "duration": 1762, "details": "UI elements adapt correctly to different viewport sizes", "timestamp": "2025-05-12T23:06:59.232Z"}, {"test": "<PERSON> Load Performance", "status": "pass", "duration": 2145, "details": "All pages loaded within acceptable time thresholds", "timestamp": "2025-05-12T23:07:00.233Z"}], "aiChat": [{"test": "AI Chat Basic Conversation", "status": "pass", "duration": 1821, "details": "AI responded correctly to basic prompts", "timestamp": "2025-05-12T23:07:01.035Z"}, {"test": "AI Chat Image Attachment", "status": "pass", "duration": 2543, "details": "AI correctly processed and responded to image attachment", "timestamp": "2025-05-12T23:07:02.236Z"}, {"test": "AI Chat Code Processing", "status": "pass", "duration": 1756, "details": "AI correctly formatted and analyzed code snippets", "timestamp": "2025-05-12T23:07:03.137Z"}, {"test": "AI Chat Streaming", "status": "pass", "duration": 2134, "details": "AI response streamed properly to the client", "timestamp": "2025-05-12T23:07:04.639Z"}, {"test": "AI Chat Context Retention", "status": "pass", "duration": 3210, "details": "AI correctly maintained context across multiple messages", "timestamp": "2025-05-12T23:07:06.641Z"}, {"test": "AI <PERSON><PERSON>", "status": "fail", "duration": 1254, "details": "The system should handle rate limiting more gracefully", "timestamp": "2025-05-12T23:07:07.241Z", "error": "AI service responded with error code 429 (rate limit)", "screenshot": null}, {"test": "AI Chat Content Filtering", "status": "pass", "duration": 1432, "details": "AI correctly filtered inappropriate content", "timestamp": "2025-05-12T23:07:07.943Z"}], "encryptedMessaging": [{"test": "Key Pair Generation", "status": "pass", "duration": 756, "details": "Successfully generated NaCl key pair", "timestamp": "2025-05-12T23:07:08.444Z"}, {"test": "Public Key Exchange", "status": "pass", "duration": 943, "details": "Successfully exchanged public keys with server", "timestamp": "2025-05-12T23:07:09.145Z"}, {"test": "Message Encryption", "status": "pass", "duration": 834, "details": "Successfully encrypted message content", "timestamp": "2025-05-12T23:07:09.746Z"}, {"test": "Message Decryption", "status": "pass", "duration": 912, "details": "Successfully decrypted received message", "timestamp": "2025-05-12T23:07:10.396Z"}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 543, "details": "Correctly generated and managed nonces for encryption", "timestamp": "2025-05-12T23:07:10.797Z"}, {"test": "Encryption Error Handling", "status": "fail", "duration": 687, "details": "The system should validate public key formats before attempting encryption", "timestamp": "2025-05-12T23:07:11.347Z", "error": "Failed to handle invalid public key format", "screenshot": null}, {"test": "Local Key Storage", "status": "pass", "duration": 623, "details": "Correctly stored and retrieved keys from local storage", "timestamp": "2025-05-12T23:07:11.798Z"}, {"test": "Encrypted UI Elements", "status": "pass", "duration": 1124, "details": "UI correctly indicates encrypted vs. unencrypted messages", "timestamp": "2025-05-12T23:07:12.600Z"}]}}