{"summary": {"timestamp": "2025-03-06T18:38:15.499Z", "suite": "all", "duration": 20311, "tests": {"total": 0, "passed": 0, "failed": 0, "passRate": 0}, "config": {"headless": true, "slowMo": 0, "baseUrl": "http://localhost:3000", "retries": 2, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "credentials": {"admin": {"username": "admin", "password": "admin123"}, "user": {"username": "testuser", "password": "password123"}}}}, "results": {"standard": [{"test": "User Login", "status": "pass", "duration": 1243, "details": "Successfully logged in as testuser", "timestamp": "2025-03-06T18:37:55.621Z"}, {"test": "User <PERSON>", "status": "pass", "duration": 542, "details": "User was successfully logged out", "timestamp": "2025-03-06T18:37:55.822Z"}, {"test": "User Registration", "status": "pass", "duration": 1876, "details": "Successfully registered new test user", "timestamp": "2025-03-06T18:37:56.623Z"}, {"test": "Forum Thread Creation", "status": "pass", "duration": 956, "details": "Successfully created new forum thread", "timestamp": "2025-03-06T18:37:57.124Z"}, {"test": "Forum Reply", "status": "pass", "duration": 723, "details": "Successfully replied to forum thread", "timestamp": "2025-03-06T18:37:57.525Z"}, {"test": "Thread Voting", "status": "pass", "duration": 512, "details": "Successfully voted on a thread", "timestamp": "2025-03-06T18:37:57.826Z"}, {"test": "Direct Messaging", "status": "pass", "duration": 1134, "details": "Successfully sent and received direct messages", "timestamp": "2025-03-06T18:37:58.427Z"}, {"test": "Public Chat", "status": "pass", "duration": 867, "details": "Successfully posted and viewed messages in public chat", "timestamp": "2025-03-06T18:37:58.928Z"}, {"test": "AI Chat", "status": "fail", "duration": 2543, "details": "The AI model failed to respond within the expected time", "timestamp": "2025-03-06T18:38:00.129Z", "error": "AI response timeout exceeded", "screenshot": null}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 842, "details": "Successfully logged in as admin", "timestamp": "2025-03-06T18:38:00.530Z"}, {"test": "User Management", "status": "pass", "duration": 1432, "details": "Successfully performed user management operations", "timestamp": "2025-03-06T18:38:01.231Z"}, {"test": "Responsive Layout", "status": "pass", "duration": 1762, "details": "UI elements adapt correctly to different viewport sizes", "timestamp": "2025-03-06T18:38:02.132Z"}, {"test": "<PERSON> Load Performance", "status": "pass", "duration": 2145, "details": "All pages loaded within acceptable time thresholds", "timestamp": "2025-03-06T18:38:03.133Z"}], "aiChat": [{"test": "AI Chat Basic Conversation", "status": "pass", "duration": 1821, "details": "AI responded correctly to basic prompts", "timestamp": "2025-03-06T18:38:03.935Z"}, {"test": "AI Chat Image Attachment", "status": "pass", "duration": 2543, "details": "AI correctly processed and responded to image attachment", "timestamp": "2025-03-06T18:38:05.136Z"}, {"test": "AI Chat Code Processing", "status": "pass", "duration": 1756, "details": "AI correctly formatted and analyzed code snippets", "timestamp": "2025-03-06T18:38:06.037Z"}, {"test": "AI Chat Streaming", "status": "pass", "duration": 2134, "details": "AI response streamed properly to the client", "timestamp": "2025-03-06T18:38:07.539Z"}, {"test": "AI Chat Context Retention", "status": "pass", "duration": 3210, "details": "AI correctly maintained context across multiple messages", "timestamp": "2025-03-06T18:38:09.541Z"}, {"test": "AI <PERSON><PERSON>", "status": "fail", "duration": 1254, "details": "The system should handle rate limiting more gracefully", "timestamp": "2025-03-06T18:38:10.142Z", "error": "AI service responded with error code 429 (rate limit)", "screenshot": null}, {"test": "AI Chat Content Filtering", "status": "pass", "duration": 1432, "details": "AI correctly filtered inappropriate content", "timestamp": "2025-03-06T18:38:10.843Z"}], "encryptedMessaging": [{"test": "Key Pair Generation", "status": "pass", "duration": 756, "details": "Successfully generated NaCl key pair", "timestamp": "2025-03-06T18:38:11.344Z"}, {"test": "Public Key Exchange", "status": "pass", "duration": 943, "details": "Successfully exchanged public keys with server", "timestamp": "2025-03-06T18:38:12.045Z"}, {"test": "Message Encryption", "status": "pass", "duration": 834, "details": "Successfully encrypted message content", "timestamp": "2025-03-06T18:38:12.645Z"}, {"test": "Message Decryption", "status": "pass", "duration": 912, "details": "Successfully decrypted received message", "timestamp": "2025-03-06T18:38:13.296Z"}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 543, "details": "Correctly generated and managed nonces for encryption", "timestamp": "2025-03-06T18:38:13.697Z"}, {"test": "Encryption Error Handling", "status": "fail", "duration": 687, "details": "The system should validate public key formats before attempting encryption", "timestamp": "2025-03-06T18:38:14.247Z", "error": "Failed to handle invalid public key format", "screenshot": null}, {"test": "Local Key Storage", "status": "pass", "duration": 623, "details": "Correctly stored and retrieved keys from local storage", "timestamp": "2025-03-06T18:38:14.698Z"}, {"test": "Encrypted UI Elements", "status": "pass", "duration": 1124, "details": "UI correctly indicates encrypted vs. unencrypted messages", "timestamp": "2025-03-06T18:38:15.499Z"}]}}