{"summary": {"timestamp": "2025-08-31T06:47:57.172Z", "suite": "encryption", "duration": 4657, "tests": {"total": 8, "passed": 7, "failed": 1, "passRate": 88}, "config": {"headless": true, "slowMo": 0, "baseUrl": "http://localhost:3000", "retries": 2, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "credentials": {"admin": {"username": "admin", "password": "admin123"}, "user": {"username": "testuser", "password": "password123"}}, "testFilter": ["encryptedMessaging", "e2eeKeyExchange", "messageEncryption", "messageDecryption", "keySecurity"]}}, "results": [{"test": "Key Pair Generation", "status": "pass", "duration": 756, "details": "Successfully generated NaCl key pair", "timestamp": "2025-08-31T06:47:53.016Z"}, {"test": "Public Key Exchange", "status": "pass", "duration": 943, "details": "Successfully exchanged public keys with server", "timestamp": "2025-08-31T06:47:53.717Z"}, {"test": "Message Encryption", "status": "pass", "duration": 834, "details": "Successfully encrypted message content", "timestamp": "2025-08-31T06:47:54.318Z"}, {"test": "Message Decryption", "status": "pass", "duration": 912, "details": "Successfully decrypted received message", "timestamp": "2025-08-31T06:47:54.968Z"}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 543, "details": "Correctly generated and managed nonces for encryption", "timestamp": "2025-08-31T06:47:55.370Z"}, {"test": "Encryption Error Handling", "status": "fail", "duration": 687, "details": "The system should validate public key formats before attempting encryption", "timestamp": "2025-08-31T06:47:55.920Z", "error": "Failed to handle invalid public key format", "screenshot": null}, {"test": "Local Key Storage", "status": "pass", "duration": 623, "details": "Correctly stored and retrieved keys from local storage", "timestamp": "2025-08-31T06:47:56.371Z"}, {"test": "Encrypted UI Elements", "status": "pass", "duration": 1124, "details": "UI correctly indicates encrypted vs. unencrypted messages", "timestamp": "2025-08-31T06:47:57.172Z"}]}