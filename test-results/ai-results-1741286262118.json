{"summary": {"timestamp": "2025-03-06T18:37:42.118Z", "suite": "ai", "duration": 7715, "tests": {"total": 7, "passed": 6, "failed": 1, "passRate": 86}, "config": {"headless": true, "slowMo": 0, "baseUrl": "http://localhost:3000", "retries": 2, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "credentials": {"admin": {"username": "admin", "password": "admin123"}, "user": {"username": "testuser", "password": "password123"}}, "testFilter": ["aiChat", "aiChatAttachments", "aiResponseStreaming", "aiSessionManagement", "aiChatDeep"]}}, "results": [{"test": "AI Chat Basic Conversation", "status": "pass", "duration": 1821, "details": "AI responded correctly to basic prompts", "timestamp": "2025-03-06T18:37:35.204Z"}, {"test": "AI Chat Image Attachment", "status": "pass", "duration": 2543, "details": "AI correctly processed and responded to image attachment", "timestamp": "2025-03-06T18:37:36.406Z"}, {"test": "AI Chat Code Processing", "status": "pass", "duration": 1756, "details": "AI correctly formatted and analyzed code snippets", "timestamp": "2025-03-06T18:37:37.307Z"}, {"test": "AI Chat Streaming", "status": "pass", "duration": 2134, "details": "AI response streamed properly to the client", "timestamp": "2025-03-06T18:37:38.814Z"}, {"test": "AI Chat Context Retention", "status": "pass", "duration": 3210, "details": "AI correctly maintained context across multiple messages", "timestamp": "2025-03-06T18:37:40.816Z"}, {"test": "AI <PERSON><PERSON>", "status": "fail", "duration": 1254, "details": "The system should handle rate limiting more gracefully", "timestamp": "2025-03-06T18:37:41.417Z", "error": "AI service responded with error code 429 (rate limit)", "screenshot": null}, {"test": "AI Chat Content Filtering", "status": "pass", "duration": 1432, "details": "AI correctly filtered inappropriate content", "timestamp": "2025-03-06T18:37:42.118Z"}]}