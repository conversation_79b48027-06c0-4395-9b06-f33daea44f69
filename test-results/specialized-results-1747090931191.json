{"summary": {"timestamp": "2025-05-12T23:02:11.191Z", "suite": "specialized", "duration": 12369, "tests": {"total": 0, "passed": 0, "failed": 0, "passRate": 0}, "config": {"headless": true, "slowMo": 0, "baseUrl": "http://localhost:3000", "retries": 2, "captureScreenshots": true, "screenshotDir": "./test-screenshots", "outputDir": "./test-results", "timeout": {"navigation": 30000, "element": 10000, "api": 15000, "ai": 45000}, "credentials": {"admin": {"username": "admin", "password": "admin123"}, "user": {"username": "testuser", "password": "password123"}}, "testFilter": ["aiChatDeep", "encryptedMessaging"]}}, "results": {"aiChat": [{"test": "AI Chat Basic Conversation", "status": "pass", "duration": 1821, "details": "AI responded correctly to basic prompts", "timestamp": "2025-05-12T23:01:59.624Z"}, {"test": "AI Chat Image Attachment", "status": "pass", "duration": 2543, "details": "AI correctly processed and responded to image attachment", "timestamp": "2025-05-12T23:02:00.825Z"}, {"test": "AI Chat Code Processing", "status": "pass", "duration": 1756, "details": "AI correctly formatted and analyzed code snippets", "timestamp": "2025-05-12T23:02:01.726Z"}, {"test": "AI Chat Streaming", "status": "pass", "duration": 2134, "details": "AI response streamed properly to the client", "timestamp": "2025-05-12T23:02:03.228Z"}, {"test": "AI Chat Context Retention", "status": "pass", "duration": 3210, "details": "AI correctly maintained context across multiple messages", "timestamp": "2025-05-12T23:02:05.230Z"}, {"test": "AI <PERSON><PERSON>", "status": "fail", "duration": 1254, "details": "The system should handle rate limiting more gracefully", "timestamp": "2025-05-12T23:02:05.831Z", "error": "AI service responded with error code 429 (rate limit)", "screenshot": null}, {"test": "AI Chat Content Filtering", "status": "pass", "duration": 1432, "details": "AI correctly filtered inappropriate content", "timestamp": "2025-05-12T23:02:06.532Z"}], "encryptedMessaging": [{"test": "Key Pair Generation", "status": "pass", "duration": 756, "details": "Successfully generated NaCl key pair", "timestamp": "2025-05-12T23:02:07.034Z"}, {"test": "Public Key Exchange", "status": "pass", "duration": 943, "details": "Successfully exchanged public keys with server", "timestamp": "2025-05-12T23:02:07.735Z"}, {"test": "Message Encryption", "status": "pass", "duration": 834, "details": "Successfully encrypted message content", "timestamp": "2025-05-12T23:02:08.336Z"}, {"test": "Message Decryption", "status": "pass", "duration": 912, "details": "Successfully decrypted received message", "timestamp": "2025-05-12T23:02:08.987Z"}, {"test": "<PERSON><PERSON>", "status": "pass", "duration": 543, "details": "Correctly generated and managed nonces for encryption", "timestamp": "2025-05-12T23:02:09.388Z"}, {"test": "Encryption Error Handling", "status": "fail", "duration": 687, "details": "The system should validate public key formats before attempting encryption", "timestamp": "2025-05-12T23:02:09.939Z", "error": "Failed to handle invalid public key format", "screenshot": null}, {"test": "Local Key Storage", "status": "pass", "duration": 623, "details": "Correctly stored and retrieved keys from local storage", "timestamp": "2025-05-12T23:02:10.389Z"}, {"test": "Encrypted UI Elements", "status": "pass", "duration": 1124, "details": "UI correctly indicates encrypted vs. unencrypted messages", "timestamp": "2025-05-12T23:02:11.190Z"}]}}