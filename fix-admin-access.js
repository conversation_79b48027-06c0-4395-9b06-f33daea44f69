/**
 * This script helps fix admin access issues by updating the user in the database
 * and forcing a session refresh
 */
const { Pool } = require('pg');
const express = require('express');
const session = require('express-session');
const app = express();
const port = 3005;

// Database setup from env
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Create a simple express server to handle admin toggle
app.use(express.json());
app.use(express.static('public'));

// Simple HTML page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Admin Access Fix</title>
      <style>
        body { font-family: monospace; max-width: 800px; margin: 0 auto; padding: 20px; }
        button { margin: 10px 0; padding: 8px 16px; background: #333; color: #0f0; border: none; cursor: pointer; }
        .log { background: #111; color: #0f0; padding: 10px; margin: 10px 0; white-space: pre; }
      </style>
    </head>
    <body>
      <h1>VintageForum Admin Access Fix</h1>
      <p>This tool can help fix admin access issues.</p>
      
      <div>
        <button id="checkUserBtn">Check Current User</button>
        <button id="refreshSessionBtn">Refresh Session</button>
      </div>
      
      <div id="output" class="log">// Logs will appear here</div>
      
      <script>
        const output = document.getElementById('output');
        
        function log(message) {
          output.textContent += "\\n" + message;
          output.scrollTop = output.scrollHeight;
        }
        
        document.getElementById('checkUserBtn').addEventListener('click', async () => {
          try {
            log('Checking current user...');
            const res = await fetch('/check-user');
            const data = await res.json();
            log(JSON.stringify(data, null, 2));
          } catch (err) {
            log('Error: ' + err.message);
          }
        });
        
        document.getElementById('refreshSessionBtn').addEventListener('click', async () => {
          try {
            log('Refreshing session...');
            const res = await fetch('/refresh-session');
            const data = await res.json();
            log(JSON.stringify(data, null, 2));
          } catch (err) {
            log('Error: ' + err.message);
          }
        });
      </script>
    </body>
    </html>
  `);
});

app.get('/check-user', async (req, res) => {
  try {
    // Check both database record and session
    const query = 'SELECT id, username, is_admin FROM users WHERE id = 1';
    const result = await pool.query(query);
    
    if (result.rows.length === 0) {
      return res.json({ error: 'User not found in database' });
    }
    
    res.json({
      message: 'User record found',
      user: result.rows[0]
    });
  } catch (error) {
    console.error('Database error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/refresh-session', async (req, res) => {
  try {
    // Ensure the user has admin access in the database
    const updateQuery = 'UPDATE users SET is_admin = true WHERE id = 1 RETURNING id, username, is_admin';
    const result = await pool.query(updateQuery);
    
    if (result.rows.length === 0) {
      return res.json({ error: 'Failed to update user' });
    }
    
    res.json({
      message: 'User updated successfully. Please log out and log back in to refresh your session.',
      user: result.rows[0],
      nextSteps: [
        '1. Go to the main application',
        '2. Log out',
        '3. Log back in with your credentials',
        '4. Try accessing the admin features again'
      ]
    });
  } catch (error) {
    console.error('Database error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Admin fix server running at http://localhost:${port}`);
  console.log('Open this URL in your browser to fix admin access issues');
});